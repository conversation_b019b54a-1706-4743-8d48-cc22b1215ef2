import logger from './logger'
declare global {
  var Acan: any
  var Agl: {
    gender: String[]
    usersRoles: String[]
    usersManagerRoles: String[]
    questionsTypes: String[]
    templateTab: String[]
    // templateCategory: String[]
    classesTypes: String[]
    collabRole: String[]
    reflectionMode: String[]
    reflectionVisible: String[]
    sessionTypeStudentCourses: String[]
    sessionTypeStudentSession: String[]
    sessionTypeTeacherSession: String[]
    workshopTypes: String[]
    sessionType: String[]
    sessionStatus: String[]
    unitSessionType: String[]
    tagsStep: String[]
    taskMode: String[]
    unitMode: String[]
    unitType: String[]
    unitToolType: String[]
    unitToolDataType: String[]
    unitToolGroupVisible: String[]
    subjectsParticipants: String[]
    classesBlockRepeat: String[]
    ServiceType: String[]
    MentoringType: String[]
    ServicePackUserType: String[]
    ServiceRoles: String[]
    ConsultantType: String[]
    ServicePackVideoType: String[]
    ServiceSalesTarget: String[]
    ServicePackageType: String[]
  }
  var FixEsm: any
  var FileType: any
  var Ser: any
  var isDev: boolean
  var isTest: boolean
  var SiteUrl: String
  var sleep: Function
  var hashToUrl: Function
  var getUTCHour: Function
  var utcDate: Function
  var dateRangeToIndexes: Function
  var LocalIp: String
  interface String {
    toFirstUpperCase: Function
  }
}
String.prototype.toFirstUpperCase = function () {
  var s = this.trim()
  return !s || s.length === 0 ? s : s[0].toUpperCase() + s.substring(1)
}

global.isDev = !['production', 'test'].includes(process.env.NODE_ENV || '')
global.isTest = process.env.NODE_ENV === 'test'
if (isDev) {
  if (isTest) {
    global.SiteUrl = `https://test.classcipe.com`
  } else {
    global.SiteUrl = `https://dev.classcipe.com`
  }
} else {
  global.SiteUrl = `https://classcipe.com`
}
global.Acan = require('../src/Acan')
global.FixEsm = require('fix-esm')
global.FileType = FixEsm.require('file-type')
global.Agl = {
  gender: ['Male', 'Female', 'Others'],
  usersRoles: ['sys', 'admin', 'teacher', 'student', 'manager'],
  usersManagerRoles: ['admin', 'academic_consultant', 'sales_manager', 'sales', 'customer_service_manager', 'customer_service', 'accountant', 'agent'],
  questionsTypes: ['text', 'choice', 'comment', 'draw', 'media', 'website', 'matching', 'filling', 'graph', 'video'],
  templateTab: [
    'Teaching scenario',
    'Interview for enrolment(Students)',
    'Interview for thesis defense',
    'Interview for enrolment(Teachers)',
    'Interview for teacher verification',
  ],
  // templateCategory: ['Beginning of the session', 'During the session', 'End of the session', 'General purpose'],
  classesTypes: ['standard', 'subject'],
  collabRole: ['read', 'write', 'comment'],
  reflectionMode: ['refl', 'comment'],
  reflectionVisible: ['all', 'school', 'private'],
  sessionTypeStudentCourses: ['unitSchoolCourses', 'pdSchoolStudentCourses', 'unitCourses', 'studentCourses'],
  sessionTypeStudentSession: [
    'videoSession',
    'session',
    'pdClassSession',
    'pdSchoolStudentWorkshop',
    'taskWorkshop',
    'taskSchoolWorkshop',
    'studentWorkshop',
    'studentToolGrade',
    'studentToolSelect',
  ],
  sessionTypeTeacherSession: ['pdSchoolTeacherWorkshop', 'pdSchoolWorkshop', 'teacherToolSchool', 'teacherToolSelect', 'workshop'],
  // need register
  workshopTypes: [
    'taskSchoolWorkshop',
    'unitSchoolCourses',
    'pdSchoolStudentWorkshop',
    'pdSchoolStudentCourses',
    'studentToolGrade',
    'pdSchoolTeacherWorkshop',
    'pdSchoolTeacherCourses',
    'teacherToolSchool',
    'taskWorkshop',
    'unitCourses',
    'studentWorkshop',
    'studentCourses',
    'studentTool',
    'workshop',
    'pdCourses',
    'teacherTool',
  ],
  sessionType: [
    'videoSession',
    // for classroom, need select students
    'session',
    'courses',
    'pdClassSession',
    'pdClassCourses',
    'studentToolSelect',
    // for school students public, need register (only for internal school students)
    'taskSchoolWorkshop',
    'unitSchoolCourses',
    'pdSchoolStudentWorkshop',
    'pdSchoolStudentCourses',
    'studentToolGrade',
    // for school educators public, need register (only for internal school teachers)
    'pdSchoolTeacherWorkshop',
    'pdSchoolTeacherCourses',
    'teacherToolSchool',
    // for school educators, need select internal school teachers
    'pdSchoolWorkshop',
    'pdSchoolCourses',
    'teacherToolSelect',
    // for students public, need register (for all students)
    'selfStudy', // to student Self study center
    'taskWorkshop',
    'unitCourses',
    'studentWorkshop',
    'studentCourses',
    'studentTool',
    // for educators public, need register (for all teachers)
    'workshop',
    'pdCourses',
    'teacherTool',
    // tool
    'tool',
    'toolSession',
    // tool
    'tool',
    'toolSession',

    // for educators booking
    'bookingTask',
    'bookingPdTask',
    // for Student booking
    'bookingStuTask',
    'bookingStuPdTask',
    'bookingStuTool',
    // 用于education consultant - interview for teacher verification
    'jobSeekerTask',
    'jobSeekerPdTask',
  ],
  sessionStatus: ['live', 'close', 'student'],
  unitSessionType: ['live', 'student'],
  tagsStep: ['basic', 'inquiry', 'applying'],
  taskMode: ['task', 'pdTask'],
  unitMode: ['unit', 'task', 'pdUnit', 'pdTask', 'video', 'tool'],
  unitType: ['FA', 'SA', 'Activity', 'IA', 'Single', 'integrated', 'UOI', 'IDU'],
  unitToolType: ['title', 'diy', 'report'],
  unitToolDataType: ['radio', 'text', 'number', 'date'],
  unitToolGroupVisible: ['all', 'teacher'],
  subjectsParticipants: ['educators', 'students'],
  classesBlockRepeat: ['week', 'month'],
  ServiceType: ['workshop', 'mentoring', 'content', 'teaching', 'correcting', 'substitute', 'serviceTask'],
  MentoringType: [
    'essay',
    'academic',
    'overseasStudy',
    'teacherTraining',
    'teacherTrainingSubject',
    'steam',
    'academicPlanning',
    'personalStatement',
    'interest',
    'thesis-defense',
    'psychology',
    'standardizedEnglishPrep',
  ], // professionalDevelopment, subject
  ServicePackUserType: ['order', 'booking', 'cancel', 'timeout', 'expired', 'refund', 'teachingAccident', 'gift', 'point', 'cash'],
  ServiceRoles: ['mentoring', 'substitute', 'correcting', 'consultant', 'onCampus', 'contentContributor'],
  ConsultantType: ['carer', 'interview', 'interviewTeacher', 'interviewThesisDefense'],
  ServicePackVideoType: ['AcademicValue', 'Features', 'QA'],
  ServiceSalesTarget: ['personal', 'school'],
  ServicePackageType: ['lecture', 'nonLecture', 'essayService'],
}
global.sleep = function (ttl: number) {
  return new Promise((res) => setTimeout(res, ttl))
}
global.hashToUrl = function (hash: string) {
  if (hash.includes('://')) return hash
  const cdnHost = isDev ? 'https://r2dev.classcipe.com' : 'https://r2.classcipe.com' // 'https://d19jvc3fkrigab.cloudfront.net/'
  if (hash.includes('/')) return `${cdnHost}/${hash.split(':')[0]}`
  return `${cdnHost}/${hash.split(':')[0]}`
}
global.getUTCHour = function (date: any) {
  const n = new Date(date)
  const day = new Date(n.getTime() + n.getTimezoneOffset() * 60000).getDay()
  return [day, n.toISOString().substring(11, 16)].join('w')
}
global.utcDate = function (utcDay: string, zone: number) {
  return new Date(new Date(utcDay).getTime() + zone * 60000)
}
global.dateRangeToIndexes = function (start: any, end: any) {
  const minutesInWeek = 7 * 24 * 60 // 10080
  const slotSize = 5
  const maxIndex = minutesInWeek / slotSize

  const toIndex = (date: any) => {
    const dayOfWeek = date.getUTCDay() // 0 = Sunday, 1 = Monday, ...
    const minutes = date.getUTCHours() * 60 + date.getUTCMinutes()
    return Math.floor((dayOfWeek * 24 * 60 + minutes) / slotSize)
  }

  let startIdx = toIndex(new Date(start))
  let endIdx = toIndex(new Date(end))

  // handle wrap-around (end before start → crosses week boundary)
  if (endIdx <= startIdx) {
    endIdx += maxIndex // extend to next week
  }

  const indexes = []
  for (let i = startIdx; i < endIdx; i++) {
    indexes.push(i % maxIndex) // wrap within [0,2015]
  }

  return indexes
}

global.LocalIp = ''
import os from 'os'
const interFaces: any = os.networkInterfaces()
for (const key of Object.keys(interFaces)) {
  for (const o of interFaces[key]) {
    if (o.family === 'IPV6') continue
    if (o.address.includes('127.0.')) continue
    if (global.LocalIp) continue
    global.LocalIp = o.address
  }
}
logger.info('local ip:', global.LocalIp, process.argv)

import app from './app'

const port = app.get('port')
const server = app.listen(port)
global.Ser = server

process.on('uncaughtException', (err: any, origin: any) => {
  logger.error('uncaughtException at: ', err)
  app
    .service('log')
    .create({type: 'node.uncaughtException', ip: global.LocalIp, origin, stack: JSON.stringify(err), body: err, msg: err?.message})
    .then()
})
process.on('unhandledRejection', (reason: any, p: any) => {
  logger.error('unhandledRejection at: Promise ', p, reason)
  let body
  try {
    body = JSON.stringify(p)
    app.service('log').create({type: 'node.unhandledRejection', ip: global.LocalIp, stack: reason.stack, body, msg: reason.message}).then()
  } catch (error) {}
})

server.on('listening', () => logger.info('Feathers application started on http://%s:%d', app.get('host'), port))

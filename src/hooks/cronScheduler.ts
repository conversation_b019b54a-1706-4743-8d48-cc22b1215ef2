/**
 * Stateless utility functions to control function execution intervals
 * Assumes the function is called every minute
 */

/**
 * Check if function should execute based on time intervals
 * @param timesPerDay - Number of times function should run per day
 * @param specificTimes - Optional specific times in HH:MM format (e.g., ['09:00', '18:00'])
 * @returns Whether the function should execute now
 */
export function shouldExecute(timesPerDay: number = 2, specificTimes?: string[]): boolean {
  const now = new Date()

  if (specificTimes && specificTimes.length > 0) {
    return checkSpecificTimes(specificTimes, now)
  } else {
    return checkEvenIntervals(timesPerDay, undefined, now)
  }
}

/**
 * Check if function should execute based on time intervals
 * @param interval - Interval in minutes
 * @param specificTimes - Optional specific times in HH:MM format (e.g., ['09:00', '18:00'])
 * @returns Whether the function should execute now
 */
export function shouldExecuteInInterval(interval: number = 30, specificTimes?: string[]): boolean {
  const now = new Date()

  if (specificTimes && specificTimes.length > 0) {
    return checkSpecificTimes(specificTimes, now)
  } else {
    return checkEvenIntervals(undefined, interval, now)
  }
}

/**
 * Check if current time matches any of the specified times
 * @param specificTimes - Array of times in HH:MM format
 * @param now - Current date/time
 * @returns boolean
 */
export function checkSpecificTimes(specificTimes: string[], now: Date = new Date()): boolean {
  const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
  return specificTimes.includes(currentTime)
}

/**
 * Check if current time falls on evenly distributed intervals
 * @param timesPerDay - Number of executions per day
 * @param now - Current date/time
 * @returns boolean
 */
export function checkEvenIntervals(timesPerDay?: number, interval?: number, now: Date = new Date()): boolean {
  const minutesInDay = 24 * 60 // 1440 minutes
  const intervalMinutes = Math.floor(interval || 0) || Math.floor(minutesInDay / (timesPerDay || 1))

  // Calculate minutes since midnight
  const currentMinutes = now.getHours() * 60 + now.getMinutes()

  // Check if current time is at an interval boundary
  return currentMinutes % intervalMinutes === 0
}

/**
 * Execute at specific hours (useful for business hours)
 * @param hours - Array of hours (0-23) when function should run
 * @param now - Current date/time
 * @returns boolean
 */
export function shouldExecuteAtHours(hours: number[] = [9, 18], now: Date = new Date()): boolean {
  const currentHour = now.getHours()
  const currentMinute = now.getMinutes()

  // Execute only at the top of the specified hours (minute 0)
  return hours.includes(currentHour) && currentMinute === 0
}

/**
 * Execute once on given dates of a month
 * @param dates - Array of dates (1-31) when function should run
 * @param time - Optional specific time in HH:MM format (default: '00:00')
 * @param now - Current date/time (for testing purposes)
 * @returns boolean - Whether the function should execute now
 *
 * Examples:
 * shouldExecuteOnMonthlyDates([1, 15]) - Execute on 1st and 15th of every month
 */
export function shouldExecuteOnMonthlyDates(dates: number[], now: Date = new Date()): boolean {
  const currentDate = now.getDate()

  // Check if today's date is in the specified dates array
  const isTargetDate = dates.includes(currentDate)

  return isTargetDate
}

/**
 * Execute only during business days (Monday-Friday)
 * @param timesPerDay - Number of times per day to execute
 * @param specificTimes - Optional specific times
 * @param now - Current date/time
 * @returns boolean
 */
export function shouldExecuteOnBusinessDays(timesPerDay: number = 2, specificTimes?: string[], now: Date = new Date()): boolean {
  const dayOfWeek = now.getDay() // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
  const isBusinessDay = dayOfWeek >= 1 && dayOfWeek <= 5 // Monday to Friday

  if (!isBusinessDay) {
    return false
  }

  return shouldExecute(timesPerDay, specificTimes)
}

/**
 * Execute only during specific date range
 * @param startDate - Start date (inclusive)
 * @param endDate - End date (inclusive)
 * @param timesPerDay - Number of times per day to execute
 * @param specificTimes - Optional specific times
 * @param now - Current date/time
 * @returns boolean
 */
export function shouldExecuteInDateRange(startDate: Date, endDate: Date, timesPerDay: number = 2, specificTimes?: string[], now: Date = new Date()): boolean {
  const currentDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const start = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate())
  const end = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate())

  if (currentDate < start || currentDate > end) {
    return false
  }

  return shouldExecute(timesPerDay, specificTimes)
}

/**
 * Create a wrapper function that only executes if timing conditions are met
 * @param timesPerDay - Number of times per day to execute
 * @param specificTimes - Optional specific times
 * @returns Function wrapper
 */
export function createIntervalWrapper(timesPerDay: number = 2, specificTimes?: string[]) {
  return function <T extends any[], R>(fn: (...args: T) => R) {
    return function (...args: T): R | undefined {
      if (shouldExecute(timesPerDay, specificTimes)) {
        return fn(...args)
      }
      return undefined
    }
  }
}

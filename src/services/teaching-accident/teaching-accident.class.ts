import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import {OrderCancellation} from '../order/lib/orderCancellation'
const {NotFound, GeneralError, BadRequest} = require('@feathersjs/errors')

export class TeachingAccident extends Service {
  app: Application
  private orderCancellation: OrderCancellation
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
    this.orderCancellation = new OrderCancellation(app)
  }

  // 未读统计
  async getUnreadCount({}: any, params: Params): Promise<any> {
    let list: any = await this.Model.find({read: false})
    let dict: any = {}
    for (let i = 0; i < list.length; i++) {
      const item = list[i]
      for (let j = 0; j < item.tags.length; j++) {
        const tag = item.tags[j]
        if (dict[tag]) {
          dict[tag]++
        } else {
          dict[tag] = 1
        }
      }
    }
    return {
      unread: list.length,
      detail: dict,
    }
  }

  async handleApprove({_id, data}: any, params: Params): Promise<any> {
    let res: any = await this.Model.findOne({_id})
    let {status, days = 0, orderId} = data
    let {endAt} = await this.app.service('suspend-class').suspend({type: 'teaching-accident', accidentId: _id, status, days, uid: res.teacher})
    res.endAt = endAt
    // if (data.tags === 'Associated task complains') {
    //   this.send(res, 'TeachingAccidentApprovedForAssociatedTask', params, days == 0 ? true : false)
    // } else {
    this.send(res, 'TeachingAccidentApproved', params, days == 0 ? true : false)
    // }
    if (!res.serviceReturn && res.serviceBooking) {
      let oldSession = await this.app.service('session').Model.findOne({_id: res.session})
      let bookingData: any = await this.app.service('service-booking').Model.findOne({_id: res.serviceBooking})
      let serviceData = await this.app.service('service-pack-user-data').Model.find({_id: {$in: bookingData.packUserData}})
      for (let i = 0; i < serviceData.length; i++) {
        const item: any = serviceData[i]
        this.app.service('service-pack-user-data').add(
          {
            packUser: res.servicePackUser,
            type: 'teachingAccident',
            times: 1,
            payMethod: 'gift',
            order: item.order,
            serviceTicket: item.serviceTicket,
            servicer: res.teacher,
            oldSession,
          },
          params
        )
      }
      await this.Model.updateOne({_id}, {serviceReturn: true})
    }
    if (!res.serviceReturn && !res.serviceBooking) {
      await this.orderCancellation.cancelOrderController(
        {
          id: orderId,
          status: 502,
          childSessions: res.sessionSnapshot?.pid
            ? [
                {
                  _id: res.session.toString(),
                  name: res.sessionSnapshot.name,
                  cover: res.sessionSnapshot.image,
                },
              ]
            : undefined,
          updateIncome: false,
        },
        params
      )

      await this.Model.updateOne({_id}, {serviceReturn: true})
    }
  }

  async extData(one: any, params?: Params) {
    one.studentInfo = await this.app.service('users').uidToInfo(one.student)
    one.teacherInfo = await this.app.service('users').uidToInfo(one.teacher)
    const suspend = await this.app.service('suspend-class').Model.findOne({accident: one._id})
    one.suspend = suspend
  }

  async send(doc: any, tpl: string, params: Params, withoutSuspension = false) {
    const {session, sessionName, student, teacher, checkReason, serviceType, serviceName, tags, evidencesStudent, endAt, servicePackUser} = doc
    const studentInfo: any = await this.app.service('users').uidToInfo(student)
    const teacherInfo: any = await this.app.service('users').uidToInfo(teacher)
    const packUserData: any = await this.app.service('service-pack-user').Model.findById(servicePackUser)
    const workshopTypes = ['taskWorkshop', 'unitCourses', 'studentWorkshop', 'studentCourses', 'workshop', 'pdCourses']
    const isWorkshop = workshopTypes.includes(serviceType)

    // to be done if the student is registered by the mobile number then ignore (TeachingAccidentReported) notification.

    // Scenario 1: Session booking - check if booked by school admin check this logic
    if (session) {
      const sessionData: any = await this.app.service('session').Model.findById(session).select('school')
      if (sessionData?.school) {
        // Check if booked by school admin
        const schoolUser: any = await this.app.service('school-user').Model.findOne({
          school: sessionData.school,
          uid: student,
          role: 'admin',
        })

        if (schoolUser) {
          studentInfo.email = schoolUser.email
          studentInfo.name = schoolUser.name
        }
      }
    }

    let url = ''
    let url2 = ''
    let url3 = ''
    let isAssociatedTask = packUserData?.snapshot?.type === 'serviceTask'
    if (tpl === 'TeachingAccidentReported') {
      if (isAssociatedTask) {
        //for booker of associated task
        if (studentInfo.roles?.includes('student')) {
          url = `${SiteUrl}/v2/study/purchased/view/${servicePackUser}?tab=myAssociatedTask&subtab=ongoing&openComplain=1`
        } else if (studentInfo.roles?.includes('teacher')) {
          url = `${SiteUrl}/v2/home/<USER>/view/${servicePackUser}?tab=myAssociatedTask&subtab=ongoing&openComplain=1`
        }
        //for teacher of associated task
        url2 = `${SiteUrl}/v2/home/<USER>/view/${servicePackUser}?tab=taskManagement&subtab=ongoing&openAppeal=1`
      }
      // to be done
      //       Scenario 1 -> navigate to the session details page of the buyer
      // - The booked service by the school admin (happens in the school account), admin's school account/substitute session - substitue teaching request/matched - details page; lecture session - calendar/details page/view complain pop up window.
      // - The booked service by the individual buyer(students/teacher), personal account/Students - My purchased/mentoring/detailed page; Teacher - I participate/mentoring/detailed page
      else {
        url = `${SiteUrl}/v2/detail/session/${session}?evaluation=true`

        url2 = `${SiteUrl}/v2/detail/session/${session}?`
        if (isWorkshop) {
          url2 += `accidentId=${doc._id}`
        }
      }
    } else if (tpl === 'TeachingAccidentApproved') {
      if (isAssociatedTask) {
        //for booker of associated task
        if (studentInfo.roles?.includes('student')) {
          url = `${SiteUrl}/v2/study/purchased/view/${servicePackUser}?tab=myAssociatedTask&subtab=ongoing&openComplain=1`
        } else if (studentInfo.roles?.includes('teacher')) {
          url = `${SiteUrl}/v2/home/<USER>/view/${servicePackUser}?tab=myAssociatedTask&subtab=ongoing&openComplain=1`
        }
        //for teacher of associated task
        url2 = `${SiteUrl}/v2/home/<USER>
      } else {
        // to be done
        // For scenario 1 the booker, will navigate to the perosnal account/session detail( students- personal account/my purchased/mentoring/ended tab/details page; teachers - personal account/I Participate/mentoring/ended tab/details page).
        // For the scenario 1, the school admin(Booker), will navigate to admin's school account/For the lecture session - Admin school account/ calendar/session details page; for the substitute session (external) / substitute request in the matched tab.
        // For scenario 1, the service provider - navigate to teacher's personal account/ I facilitate/ mentoring or substitute/ended tab/details page.
        url = `${SiteUrl}/v2/detail/session/${session}?evaluation=true`
        url2 = `${SiteUrl}/v2/detail/session/${session}`
      }
      url3 = `${SiteUrl}/help/#/main/terms`
    } else if (tpl === 'TeachingAccidentRejected') {
      if (isAssociatedTask) {
        //for booker of associated task
        if (studentInfo.roles?.includes('student')) {
          url = `${SiteUrl}/v2/study/purchased/view/${servicePackUser}?tab=myAssociatedTask&subtab=ongoing&openReject=1`
        } else if (studentInfo.roles?.includes('teacher')) {
          url = `${SiteUrl}/v2/home/<USER>/view/${servicePackUser}?tab=myAssociatedTask&subtab=ongoing&openReject=1`
        }
        //for teacher of associated task
        url2 = `${SiteUrl}/v2/home/<USER>
      } else {
        // to be done
        // For scenario 1 the buyer , will navigate to the perosnal account/session detail( students- personal account/my purchased/mentoring/ended tab/details page; teachers - personal account/I Participate/mentoring/ended tab/details page).

        // For the scenario 1, the school admin, will navigate to admin's school account/For the lecture session - Admin school account/ calendar/session details page; for the substitute session (external) / substitute request in the matched tab.
        // For scenario 1, the service provider - navigate to teacher's personal account/ I facilitate/ mentoring or substitute/ended tab/details page.

        url = `${SiteUrl}/v2/detail/session/${session}`
        url2 = `${SiteUrl}/v2/detail/session/${session}`
      }
      url3 = `${SiteUrl}/help/#/main/terms`
    }

    let needSuffix = false
    if (tpl === 'TeachingAccidentReported' || tpl === 'TeachingAccidentApproved' || tpl === 'TeachingAccidentRejected') {
      needSuffix = true
    }
    // 发给学生
    await this.app.service('notice-tpl').mailto(
      tpl + `${needSuffix ? 'Student' : ''}`,
      studentInfo.email,
      {
        username: studentInfo.name.join(' '),
        session_name: packUserData?.associatedTask ? packUserData.snapshot.name : sessionName,
        check_reason: checkReason,
        url,
        service_type: serviceType,
        service_name: serviceName,
        tags: tags.join(','),
        reason: evidencesStudent[0].content,
        start: new Date().toLocaleDateString(),
        end: new Date(endAt).toLocaleDateString(),
        student_name: studentInfo.name.join(' '),
        teacher_name: teacherInfo.name.join(' '),
        url2,
        url3,
      },
      params.user?._id
    )
    // 发给老师
    await this.app.service('notice-tpl').mailto(
      tpl + `${needSuffix ? 'Teacher' : ''}`,
      teacherInfo.email,
      {
        username: teacherInfo.name.join(' '),
        session_name: isAssociatedTask ? packUserData.snapshot.name : sessionName,
        check_reason: checkReason,
        url: packUserData?.associatedTask || isWorkshop ? url2 : url,
        service_type: serviceType,
        service_name: serviceName,
        tags: tags.join(','),
        reason: evidencesStudent[0].content,
        start: new Date().toLocaleDateString(),
        end: new Date(endAt).toLocaleDateString(),
        student_name: studentInfo.name.join(' '),
        teacher_name: teacherInfo.name.join(' '),
        url2,
        url3,
      },
      params.user?._id
    )

    // 有停课,发送额外通知给老师
    if (!withoutSuspension && tpl === 'TeachingAccidentApproved') {
      await this.app.service('notice-tpl').mailto(
        'TeachingAccidentApprovedWithSuspensionTeacher',
        teacherInfo.email,
        {
          username: teacherInfo.name.join(' '),
          session_name: isAssociatedTask ? packUserData.snapshot.name : sessionName,
          check_reason: checkReason,
          url,
          service_type: serviceType,
          service_name: serviceName,
          tags: tags.join(','),
          reason: evidencesStudent[0].content,
          start: new Date().toLocaleDateString(),
          end: new Date(endAt).toLocaleDateString(),
          student_name: studentInfo.name.join(' '),
          teacher_name: teacherInfo.name.join(' '),
          url2,
        },
        params.user?._id
      )
    }
  }

  async shouldGenerateIncome(accident: any, sessionDoc: any, orderId: string) {
    if (accident.status === 'pending') return

    const session: string = accident.session
    const servicer: string = accident.teacher
    const booker: string = accident.student
    const businessId: string = accident.serviceBooking || `${session}-${orderId}`

    const expIncome: any = await this.app.service('income-log').Model.findOne({
      uid: servicer,
      businessId,
      category: 'teaching_service',
      status: 0,
    })
    if (!expIncome) return
    // check takeaway on for mentoring, not workshop
    const takeaway: any =
      accident.serviceBooking && accident.status === 'rejected'
        ? await this.app.service('session-takeaway-snapshot').Model.findOne({session, uid: booker})
        : {hash: 1}
    if (!takeaway || !takeaway.hash) {
      // todo: send reminder notification
      this.app.service('session-takeaway-snapshot').sendTakeawayReminder({servicer, sessionName: sessionDoc.name, booker, takeawayId: takeaway?._id.toString()})
      // update income log to show takeaway banner
      await this.app.service('income-log').Model.updateOne(
        {
          _id: expIncome._id.toString(),
        },
        {
          $set: {
            'eventDetails.takeaway': {
              id: takeaway?._id.toString(),
              session: session,
              uid: booker,
            },
            'eventDetails.session': session,
          },
        }
      )
      return
    }

    let value = expIncome.value
    let notes: string | undefined
    let source = ''
    let snapshot
    if (accident.status === 'approved') {
      value = 0
      notes = 'Amount change due to Teaching accident'
    }

    if (accident.serviceBooking) {
      const booking: any = await this.app.service('service-booking').Model.findById(accident.serviceBooking).select(['booker', 'servicer', 'type', 'packUser'])
      if (!booking) return

      source = 'booking_confirmed'
      snapshot = booking
    } else {
      source = 'workshop'
      snapshot = sessionDoc
    }

    this.app.service('income-log').generateIncome({
      source,
      snapshot,
      otherDetails: {
        query: {
          _id: expIncome._id.toString(),
        },
        payload: {
          $set: {
            status: 1,
            value,
            notes,
            'eventDetails.sessionStatus': 'ended',
          },
        },
      },
    })
  }
}

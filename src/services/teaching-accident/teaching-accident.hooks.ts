import {Service} from 'feathers-mongoose'
import {HooksObject, HookContext} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
const {NotFound, GeneralError, BadRequest} = require('@feathersjs/errors')
// Don't remove this comment. It's needed to format import lines nicely.

import hook from '../../hook'
const {authenticate} = authentication.hooks

export const typeMap: any = {
  ['workshop']: 'Premium workshop',
  ['_content']: 'Premium content',
  ['mentoring:essay']: 'Essay',
  ['mentoring:steam']: 'STEAM',
  ['mentoring:overseasStudy']: 'Overseas study',
  ['mentoring:academic']: 'Academic',
  ['mentoring:teacherTraining']: 'Teacher training',
  ['substitute']: 'Substitute teacher',
  ['correcting']: 'Correcting service',
}

export default {
  before: {
    all: [authenticate('jwt')],
    find: [],
    get: [hook.toClass],
    create: [
      async (d: HookContext) => {
        let {booking} = d.data

        const session = await d.app
          .service('session')
          .Model.findById({_id: d.data.session})
          .select(['image', 'name', 'booking', 'discount', 'uid', 'type', 'pid', 'authId', 'ended'])
        if (Date.now() - new Date(session.ended).getTime() > 24 * 60 * 60 * 1000) {
          throw new Error(`It's been 24 hours since the course ended, you can't leave a review anymore.`)
        }
        if (booking) {
          let bookingData = await d.app.service('service-booking').get(booking)
          let servicePack = await d.app.service('service-pack-user').Model.findById(bookingData.packUser).select('snapshot').lean()
          let key = servicePack ? `${servicePack.type}${servicePack.mentoringType ? ':' + servicePack.mentoringType : ''}` : ''
          d.data.serviceBooking = booking
          d.data.servicePackUser = bookingData.packUser
          d.data.service = servicePack._id
          d.data.serviceType = 'mentoring'
          d.data.serviceTypeName = `Mentor service${typeMap[key] ? '-' + typeMap[key] : ''}`
          d.data.serviceName = servicePack.name
        } else {
          const workshopTypes = ['taskWorkshop', 'unitCourses', 'studentWorkshop', 'studentCourses', 'workshop', 'pdCourses']

          d.data.serviceType = workshopTypes.includes(session.type) ? 'workshop' : 'mentoring'
          d.data.serviceTypeName = workshopTypes.includes(session.type) ? 'Workshop' : 'Mentoring'
        }

        d.data.sessionSnapshot = Acan.clone(session)
        d.data.sessionEndedAt = session.ended
      },
    ],
    update: [],
    patch: [
      async (d: HookContext) => {
        let doc: any
        let query = d.params.query || {}
        const userId = d.params?.user?._id

        if (!userId) {
          throw new Error('Authentication error: User not found.')
        }
        if (!d.id && !query?.session) {
          throw new Error(`Bad request`)
        }
        if (d.id === null) {
          doc = await d.service.Model.findOne({session: query.session}).select(['sessionEndedAt', 'student', 'teacher', 'status'])
        } else {
          doc = await d.service.Model.findById(d.id).select(['sessionEndedAt', 'student', 'teacher', 'status'])
        }

        // for a single document only
        if (d.id !== null && d.data.status && doc.status !== 'pending') {
          throw new Error(`Processed reports cannot be modified.`)
        }

        // ceck if not stu tea check sys
        if (d.params?.user?._id === doc.teacher || d.params?.user?._id === doc.student) {
          if (d.data.status) {
            throw new Error('Bad request')
          }

          const deadlineHours = userId === doc.teacher ? 48 : 24
          if (Date.now() - new Date(doc.sessionEndedAt).getTime() > deadlineHours * 60 * 60 * 1000) {
            throw new Error(`The ${deadlineHours}-hour deadline has passed.`)
          }
        } else {
          if (!('status' in d.data)) {
            return d
          }
          if (!hook.managerRoleHas(['sys', 'admin', 'academic_consultant', 'customer_service_manager', 'customer_service'])(d)) {
            throw new Error(`Bad request`)
          }
          if (Date.now() - new Date(doc.sessionEndedAt).getTime() <= 48 * 60 * 60 * 1000) {
            throw new Error(`Descision cannot be prosseed before 48 hours after the course ended.`)
          }
          query.status = 'pending'
        }

        if (d.data.status === 'approved') {
          // 审核通过
          d.params.accident = 1
        }
        if (d.data.status === 'rejected') {
          // 申诉成功
          d.params.accident = -1
        }
        if (!d.id && d.data.$addToSet?.evidencesTeacher) {
          query.evidencesTeacher = {$size: 0}
        }
        d.params.query = query
      },
    ],
    remove: [],
  },

  after: {
    all: [],
    find: [
      async (d: HookContext) => {
        for (let i = 0; i < d.result.data.length; i++) {
          await d.service.extData(d.result.data[i])
        }
      },
    ],
    get: [
      async (d: HookContext) => {
        if (hook.classExist(d)) {
          return d
        }
        await d.service.extData(d.result)
      },
    ],
    create: [
      async (d: HookContext) => {
        let {booking} = d.data
        let {_id, tags} = d.result
        if (booking) {
          await d.app.service('service-booking').patch(booking, {accident: {id: _id, status: 'pending', tags}})
        }

        if (d.data?.evidencesStudent?.length) {
          d.service.send(d.result, 'TeachingAccidentReported', d.params)
        }
      },
    ],
    update: [],
    patch: [
      async (d: HookContext) => {
        if (d.data?.$addToSet?.evidencesStudent) {
          d.service.send(d.result, 'TeachingAccidentReported', d.params)
        }
        // 审核确认后 需要重新更新统计
        const results = Array.isArray(d.result) ? d.result : [d.result]
        if (results.length === 0) {
          return d
        }

        let sessionDoc: any = results[0].sessionSnapshot
        if (!results[0]?.serviceBooking && sessionDoc.pid && (d.data.status === 'approved' || d.data.status === 'rejected')) {
          const parentSession = await d.app.service('session').Model.findById(sessionDoc.pid).select(['authId', 'reg'])
          sessionDoc.authId = parentSession.authId
          sessionDoc.reg = parentSession.reg
        }

        results.map((item) => {
          if (d.params.accident) {
            d.app.service('service-conf').upRating({
              _id: item.teacher,
              accident: d.params.accident,
            })
          }
          let {status} = d.data
          let orderId: any = null
          if (status === 'approved' || status === 'rejected') {
            const matchingReg = sessionDoc.reg?.find((regEntry: any) => regEntry._id.toString() === item.student.toString())
            orderId = matchingReg?.order
          }

          if (status === 'approved') {
            d.service.handleApprove(
              {
                _id: item._id,
                data: {...d.data, orderId},
              },
              d.params
            )
          }

          if (status === 'rejected') {
            d.service.send(item, 'TeachingAccidentRejected', d.params)
            d.app.service('suspend-class').suspend({
              type: 'teaching-accident',
              accidentId: item._id,
              status,
              days: 0,
              uid: item.teacher,
            })
          }

          if (item.serviceBooking) {
            d.app.service('service-booking').patch(item.serviceBooking, {
              'accident.status': status,
            })
          }

          if (status === 'approved' || status === 'rejected') {
            d.service.shouldGenerateIncome(item, sessionDoc, orderId)
          }
        })
      },
    ],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}

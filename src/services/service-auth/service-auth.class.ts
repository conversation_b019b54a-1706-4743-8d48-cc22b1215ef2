import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import {GeneralError} from '@feathersjs/errors'

import {
  sysCurriculumMap,
  VerificationMap,
  getTitleLabel,
  VerificationList,
  sortBySysCurriculum,
  sysCurriculumKeys,
  getVerificationKey,
  OverseasStudyMap,
  getTopicData,
} from './unit/dict' // @ts-ignore
import hook from '../../hook'

export class ServiceAuth extends Service {
  app: Application
  selectList: String[]
  allowRoles: String[]
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.allowRoles = ['customer_service', 'customer_service_manager', 'academic_consultant', 'admin']
    this.selectList = [
      'uid',
      'enable',
      'status',
      'readyForJob',
      'type',
      'mentoringType',
      'serviceRoles',
      'countryCode',
      'curriculum',
      'subject',
      'gradeGroup',
      'grades',
      'topic',
      'desc',
      'createdAt',
      'updatedAt',
      'qualification',
      'feedback',
      'follower',
      'followedAt',
      'releasedAt',
      'schoolOfFollower',
      'tags',
      'importUsers',
      'unit',
      'unitSnapshot._id',
      'unitSnapshot.name',
      'unitSnapshot.mode',
      'unitSnapshot.cover',
      'approval',
    ]
    this.app = app
  }
  async getCountType(query: any, params: Params) {
    return await this.Model.aggregate([
      {$match: {}},
      {
        $group: {
          _id: {
            type: '$type',
            mentoringType: '$mentoringType',
            status: '$status',
          },
          count: {$sum: 1},
        },
      },
    ])
  }
  // 统计认证老师数量，用于发布服务包看
  async getGroups(query: any, params: Params) {
    return await this.groups(query)
  }
  // 统计认证老师数量，用于发布服务包看
  async groups(query: any, enableRs?: any) {
    delete query.serviceRoles
    const arr = await this.Model.aggregate([
      {$match: query},
      {
        $group: {
          _id: {
            countryCode: '$countryCode',
            curriculum: '$curriculum',
            subject: '$subject',
            gradeGroup: '$gradeGroup',
            serviceRoles: '$serviceRoles',
            status: '$status',
            enable: '$enable',
            topic: '$topic._id',
          },
          count: {$sum: 1},
        },
      },
    ])
    const rs: any = {
      countryCode: {},
      curriculum: {},
      subject: {},
      gradeGroup: {},
      serviceRoles: {},
      topic: {},
    }
    for (const {_id, count} of arr) {
      for (const key of Object.keys(rs)) {
        if (_id[key]) {
          if (!rs[key][_id[key]]) rs[key][_id[key]] = []
          rs[key][_id[key]].push({..._id, count})
        }
      }
    }
    rs.enableRs = enableRs
    return rs
  }

  // 统计认证课件数量
  // query: {type, curriculum, subject}
  async getGroupTopic(query: any) {
    delete query['topic._id']
    query['unit._id'] = {$exists: true}
    query.status = 2
    const arr = await this.Model.aggregate([
      {$match: query},
      {
        $group: {
          _id: {
            topic: '$topic._id',
          },
          count: {$sum: 1},
        },
      },
    ])
    const rs: any = {}
    for (const {_id, count} of arr) {
      for (const topic of _id.topic) {
        if (!rs[topic]) rs[topic] = count
        else rs[topic] += count
      }
    }
    return rs
  }
  // 统计认证老师数量，用于发布服务包看
  // async getStats(query: any, params: Params) {
  //   const arr: any = await this.Model.find(query).limit(1000)
  //   const rs: any = {
  //     countryCode: {},
  //     curriculum: {},
  //     subject: {},
  //     gradeGroup: {},
  //   }
  //   for (const one of arr) {
  //     for (const key of Object.keys(rs)) {
  //       if (Acan.isEmpty(one[key])) continue
  //       if (Array.isArray(one[key])) {
  //         for (const ck of one[key]) {
  //           if (!rs[key][ck]) rs[key][ck] = {}
  //           if (!rs[key][ck][one.status]) rs[key][ck][one.status] = 0
  //           rs[key][ck][one.status]++
  //         }
  //       } else {
  //         if (!rs[key][one[key]]) rs[key][one[key]] = {}
  //         if (!rs[key][one[key]][one.status]) rs[key][one[key]][one.status] = 0
  //         rs[key][one[key]][one.status]++
  //       }
  //     }
  //   }
  //   return rs
  // }
  // 后台用户和本人除外，限制ppt页数
  async extPagesLimit(o: any, params: Params) {
    if (params.query?.unlocked) return // 认证课排课的时候不限制
    if (params.user?._id === o.uid) return // 本人不限制
    if (hook.roleHas(this.allowRoles)({params})) return // 后台用户不限制
    if (o.unitSnapshot && Acan.isEmpty(o.unitSnapshot?.link)) {
      const pageNum = o.unitSnapshot.pageNum
      if (o.unitSnapshot.pages) o.unitSnapshot.pages.length = pageNum > 10 ? 3 : Math.ceil(pageNum * 0.3)
    } else {
      if (!o.linkSnapshot) return
      for (const key in o.linkSnapshot) {
        const pageNum = o.linkSnapshot[key].pageNum
        if (o.linkSnapshot[key].pages) o.linkSnapshot[key].pages.length = pageNum > 10 ? 3 : Math.ceil(pageNum * 0.3)
      }
    }
  }
  async ext(o: any) {
    if (o.uid) o.owner = await this.app.service('users').uidToInfo(o.uid)
    if (o.unitSnapshot?.uid) {
      o.unitSnapshot.owner = await this.app.service('users').uidToInfo(o.unitSnapshot.uid)
    }
    if (o.approval?.approver) {
      Object.assign(o.approval, await this.app.service('users').uidToInfo(o.approval.approver))
    }
  }
  // 获取单个老师的已认证数据
  getListByUid({uid}: any) {
    return this.Model.find({uid, status: 2}).select(this.selectList)
  }

  async send(doc: any, tpl: string, params: Params): Promise<any> {
    const user: any = await this.app.service('users').uidToInfo(doc.uid)
    let name = await this.getAuthName(doc)
    let url = ''
    if (tpl === 'verificationrejected') url = `${SiteUrl}/v2/account/teacher/auth/edit/verification?dialogId=${doc._id}`
    if (tpl === 'VerificationStatusChanged') url = `${SiteUrl}/v2/account/teacher/auth/introduction`
    if (tpl === 'verificationapproved') url = `${SiteUrl}/v2/com/agreement/service_provider/verification`
    if (tpl === 'ReminderOfInterview(Teachers)') url = `${SiteUrl}/v2/service/pack/${doc.interviewPack}`
    if (tpl === 'Approval/RejectionOfPremiumContent') url = `${SiteUrl}/v2/account/teacher/auth/edit/verification?dialogId=${doc._id}`
    if (tpl === 'SetTheServiceAvailabilityUnderTheCalendar') url = `${SiteUrl}/v2/account/teacher/auth/view/availability`

    let result = 'Approved'
    if (doc.status == -1) {
      result = 'Rejected'
    }
    return await this.app.service('notice-tpl').mailto(
      tpl,
      user.email,
      {
        username: user.name.join(' '),
        name: name.join('-'),
        name_under: name.slice(0, name.length - 1).join('-'),
        url,
        reason: doc.reason,
        service_type: name[name.length - 1],
        result,
        unit_name: doc?.unit?.name,
      },
      params.user?._id
    )
  }

  // 获取link下的快照
  async getUnitSnapshotLink(snapshot: any, linkSnapshot: any, params: Params) {
    for (const group of snapshot.linkGroup) {
      for (const o of snapshot.link) {
        if (o.group !== group._id) continue
        if (linkSnapshot[o.id]) continue // 去重
        const rs = await this.app.service('unit').snapshot({_id: o.id}, params)
        if (!rs) return Promise.reject(new GeneralError(`Link content:${o.id} not found`))
        if (rs.isEdit) return Promise.reject(new GeneralError(`Link content:${rs.name} in editing`))
        if (!rs.filled) return Promise.reject(new GeneralError(`Link content:${rs.name} ${o.id} incomplete`))
        linkSnapshot[o.id] = rs
        if (!Acan.isEmpty(rs.link) && !Acan.isEmpty(rs.linkGroup)) await this.getUnitSnapshotLink(rs, linkSnapshot, params)
      }
    }
  }
  // 生成整个 unit 快照
  async getUnitSnapshot({_id, unit}: any, params: Params) {
    const unitSnapshot = Acan.clone(await this.app.service('unit').snapshot({_id: unit}, params))
    if (unitSnapshot.isEdit) return Promise.reject(new GeneralError(`Unit:${unitSnapshot.name} in editing`))
    if (!unitSnapshot.filled) return Promise.reject(new GeneralError(`Unit:${unitSnapshot.name} incomplete`))
    const linkSnapshot: any = {}
    await this.getUnitSnapshotLink(unitSnapshot, linkSnapshot, params)
    const $set: any = {unitSnapshot, linkSnapshot}
    // 课件认证 计算出价格
    if (['task', 'pdTask'].includes(unitSnapshot.mode)) {
      $set['unit.price'] = unitSnapshot.questions.length * 20
    }
    return await this.Model.updateOne({_id}, {$set})
  }
  // 查询已经认证的精品课数据
  async getUnit({}: any, params: Params) {
    const query: any = params.query ?? {}
    query['unitSnapshot._id'] = {$ne: null}
    const unitSelect = this.app.service('unit').selectList.map((v: String) => 'unitSnapshot.' + v)
    query.$select = [...this.selectList, ...unitSelect, 'unitSnapshot._id']
    return await hook.find(this.Model, query)
  }
  async getAuthName(doc: any) {
    let key = `${doc.type}${doc.mentoringType ? ':' + doc.mentoringType : ''}`

    const subject: any = await this.app.service('subjects').find({query: {uid: '1', $limit: 500, isLib: true}})
    let sysList = subject?.data || []
    const sysSubjectMap = sysList.reduce((acc: any, cur: any) => {
      if (cur?._id) acc[cur._id] = cur
      return acc
    }, {})

    const keys = VerificationList.map((e) => e.value)
    const result: any = {}
    const callbacks = []
    for (const key of keys) {
      callbacks.push(this.getTeacherVerificationConfig(`Service:${key}`))
    }
    const resArray = await Promise.all(callbacks)
    keys.forEach((key, i) => {
      if (resArray[i]) result[key] = resArray[i]
    })
    let allTeacherVerificationConfig = result

    const sysSubjectMapByCurriculum: any = () => {
      const result: any = {}
      sysCurriculumKeys.forEach((code) => {
        const filtered = sysList.filter((e: any) => !e?.del).filter((e: any) => e?.curriculum?.includes(code)) ?? []
        result[code] = filtered
      })
      return result
    }

    const auth: any = await this.app.service('service-auth').Model.find()
    let list = auth ?? []
    list = list.sort((a: any, b: any) => +new Date(b.updatedAt) - +new Date(a.updatedAt))

    let userVerificationMap: any = {}
    for (const e of list) {
      if (!e?.type) continue
      let key = getVerificationKey(e)
      if (!userVerificationMap[key]) userVerificationMap[key] = []
      userVerificationMap[key].push(e)
    }

    let name = []
    let label = getTitleLabel(key)
    name.unshift(label)
    if (key == 'workshop') {
      if (doc.curriculum !== 'pd') {
        name.unshift('Academic')
        name.unshift(sysCurriculumMap?.[doc.curriculum]?.label)
      }
      if (doc.curriculum === 'pd') {
        name.unshift(sysCurriculumMap['pd']?.label)
        name.unshift(sysSubjectMap?.[doc.subject]?.name)
      }
    }
    if (key == 'correcting') {
      name.unshift(sysCurriculumMap[doc.curriculum]?.label)
      name.unshift(sysSubjectMap?.[doc.subject]?.name)
    }
    if (key == 'substituteAcademic') {
      name.unshift(sysCurriculumMap[doc.curriculum]?.label)
      name.unshift(sysSubjectMap?.[doc.subject]?.name)
    }
    if (key == 'substituteService') {
      let cur = sysSubjectMapByCurriculum()['pd'].find((e: any) => e._id == doc.subject)
      let curVerification = userVerificationMap[key].find((e: any) => e.subject == doc.subject)

      if (cur.name.toLowerCase() === 'teacher training') {
        name.unshift(cur.name)
        name.unshift(curVerification.topic?.[0]?.label?.[curVerification.topic?.[0]?.label?.length - 1])
      }
      if (cur.name.toLowerCase() === 'overseas study') {
        name.unshift(cur.name)
        name.unshift(OverseasStudyMap[doc.countryCode]?.label)
      }
      if (cur.name.toLowerCase() === 'essay') {
        name.unshift(cur.name)
        name.unshift(curVerification.topic?.[0]?.label?.[curVerification.topic?.[0]?.label?.length - 1])
      }
      if (cur.name.toLowerCase() === 'steam') {
        name.unshift(cur.name)
        name.unshift(curVerification.topic?.[0]?.label?.[curVerification.topic?.[0]?.label?.length - 1])
      }
    }
    if (key == 'mentoring:academic') {
      // name.push(getTitleLabel(key))
      name.unshift(sysCurriculumMap[doc.curriculum]?.label)
      name.unshift(sysSubjectMap?.[doc.subject]?.name)
    }
    if (key == 'mentoring:overseasStudy') {
      // name.push(getTitleLabel(key))
      name.unshift(OverseasStudyMap?.[doc.countryCode]?.label)
    }
    if (key == 'mentoring:essay') {
      let cur = sysSubjectMapByCurriculum()['pd'].find((e: any) => e._id == doc.subject)
      let curVerification = userVerificationMap[key].find((e: any) => e.subject == doc.subject)
      // name.push(getTitleLabel(key))
      name.unshift(curVerification.topic?.[0]?.label?.[curVerification.topic?.[0]?.label?.length - 1])
    }
    if (key == 'mentoring:teacherTraining') {
      let cur = sysSubjectMapByCurriculum()['pd'].find((e: any) => e._id == doc.subject)
      let curVerification = userVerificationMap[key].find((e: any) => e.subject == doc.subject)
      // name.push(getTitleLabel(key))
      name.unshift(curVerification.topic?.[0]?.label?.[curVerification.topic?.[0]?.label?.length - 1])
    }
    if (key == 'mentoring:steam') {
      let curVerification = userVerificationMap[key].find((e: any) => e.subject == doc.subject)
      name.unshift(curVerification.topic?.[0]?.label?.[curVerification.topic?.[0]?.label?.length - 1])
    }
    if (key == 'mentoring:teacherTrainingSubject') {
      let curVerification = userVerificationMap[key].find((e: any) => e.subject == doc.subject)
      name.unshift(...curVerification.topic?.[0]?.label.reverse())
    }
    if (key == 'mentoring:academicPlanning') {
      let curVerification = userVerificationMap[key].find((e: any) => e.subject == doc.subject)
      name.unshift(curVerification.topic?.[0]?.label?.[curVerification.topic?.[0]?.label?.length - 1])
    }
    if (key == 'mentoring:personalStatement') {
      let curVerification = userVerificationMap[key].find((e: any) => e.subject == doc.subject)
      name.unshift(curVerification.topic?.[0]?.label?.[curVerification.topic?.[0]?.label?.length - 1])
    }
    if (key == 'mentoring:interest') {
      let curVerification = userVerificationMap[key].find((e: any) => e.subject == doc.subject)
      name.unshift(curVerification.topic?.[0]?.label?.[curVerification.topic?.[0]?.label?.length - 1])
    }
    return name
  }
  async getTeacherVerificationConfig(key: any) {
    try {
      let res = null
      res = await this.app
        .service('conf')
        .get(key)
        .catch((e) => {
          if (e.code === 404) return this.app.service('conf').create({_id: key, val: {}})
          return null
        })
      if (res?.curriculum?.length) res.curriculum = sortBySysCurriculum(res.curriculum)
      return res
    } catch (error) {
      console.error(error)
    }
  }
  async authVerifyInviter(doc: any) {
    let {uid, _id, inviter} = doc
    let auth = await this.Model.find({uid, status: 2})
    let user = await this.app.service('users').Model.findOne({_id: uid})
    let conf: any = await this.app.service('service-conf').Model.findOne({_id: uid})
    if (!conf?.inviter) {
      await this.app.service('service-conf').Model.updateOne({_id: uid}, {$set: {inviter: inviter}})
    }
    if (auth.length == 1) {
      await this.app.service('point-log').getAddLog({
        inviter: inviter,
        tab: 'earn',
        source: 'reward',
        category: 'verify',
        businessId: _id,
        snapshot: user,
      })
    }
  }
  // 用户留言
  async patchMessage({_id, message}: any, params?: Params) {
    return await this.Model.updateOne({_id}, {'feedback.message': message, 'feedback.read': false, 'feedback.date': new Date()})
  }
  // 后台回复
  async patchReply({_id, reply}: any, params?: Params) {
    return await this.Model.updateOne({_id}, {'feedback.reply': reply, 'feedback.replyDate': new Date()})
  }
  // 统计被多少发布的服务包关联
  async getCountPackUse({_id}: any, params: Params) {
    return await this.app.service('service-pack').Model.count({'contentOrientated.premium': _id, status: true})
  }

  async extFollower(doc: any, params?: Params) {
    if (doc.follower) {
      if (doc.schoolOfFollower) {
        doc.followerInfo = await this.app.service('school-user').Model.findOne({uid: doc.follower, school: doc.schoolOfFollower})
      } else {
        doc.followerInfo = await this.app.service('users').Model.findOne({_id: doc.follower})
      }
    }
  }
  async getGroupByFollower({type = 'content'}: any, params: Params): Promise<any> {
    let match: any = {
      follower: {$exists: true},
    }
    if (type) {
      match.type = type
    }
    let list = await this.Model.aggregate([
      {$match: match},
      {
        $group: {
          _id: {follower: '$follower', schoolOfFollower: '$schoolOfFollower'},
          follower: {$first: '$follower'},
          schoolOfFollower: {$first: '$schoolOfFollower'},
          count: {$sum: 1},
        },
      },
      {$sort: {count: -1}},
    ])
    for (let i = 0; i < list.length; i++) {
      await this.extFollower(list[i])
    }
    return list
  }
  // async getGroupByProduct({followed = true}: any, params: Params): Promise<any> {
  //   let match: any = {}
  //   if (followed) {
  //     match.follower = {$exists: true}
  //   } else {
  //     match.follower = {$exists: false}
  //   }
  //   return await this.Model.aggregate([
  //     {$match: match},
  //     {
  //       $group: {
  //         _id: '$serviceType',
  //         servicePack: {$first: '$servicePack'},
  //         servicePackName: {$first: '$servicePackName'},
  //         count: {$sum: 1},
  //       },
  //     },
  //     {$sort: {count: -1}},
  //   ])
  // }
  async autoRelease() {
    this.Model.find({status: 1, followedAt: {$lt: Date.now() - 14 * 24 * 3600 * 1000}}).then(async (rs: any) => {
      for (let i = 0; i < rs.length; i++) {
        const item = rs[i]
        await this.Model.updateOne({_id: item._id}, {$unset: {follower: '', followedAt: ''}, releasedAt: new Date()})
      }
    })
  }
  async cron1({}: any, params?: Params): Promise<any> {
    this.autoRelease()
  }
  async _Find(query: any, params: Params) {
    const rs: any = {total: 0, skip: query.$skip || 0, limit: query.$limit || 10}
    rs.total = await this.Model.count(query)
    rs.data = Acan.clone(await this.Model.find(query).select(query.$select).sort({_id: -1}).skip(rs.skip).limit(rs.limit))
    for (const o of rs.data) {
      await this.ext(o)
    }
    if (isDev) {
      rs.query = query
    }
    return rs
  }
  // 认证精品课列表
  async getCloudList(query: any, params: Params) {
    const uid = params.user?._id
    // query.importUsers = {$ne: uid}
    query.uid = {$ne: uid}
    query.status = 2
    query.type = 'content'
    query['unitSnapshot._id'] = {$exists: true}

    const unitSelect = this.app.service('unit').selectList.map((v: String) => 'unitSnapshot.' + v)
    query.$select = [...this.selectList, ...unitSelect, 'unitSnapshot._id', 'unitSnapshot.questions']
    const rs: any = await this._Find(query, params)
    // if (!query.$skip || query.$skip == 0) {
    //   query.importUsers = uid
    //   const data: any = await this.Model.find(query).select(query.$select).limit(1000).lean()
    //   if (data) {
    //     for (const o of data) {
    //       await this.ext(o)
    //     }
    //     rs.data.unshift(...data)
    //   }
    //   rs.has = data.length
    // }
    return rs
  }

  // 面试手动return
  async getInterviewReturn({id}: any): Promise<any> {
    let serviceAuthData: any = await this.Model.findOne({_id: id})
    let {interviewInvited, takeaway} = serviceAuthData
    if (!interviewInvited) {
      return Promise.reject(new GeneralError('The status of the application can not be returned.'))
    }
    // takeaway解绑
    let path = Acan.parseURL(takeaway).path
    let takeawayId = path.split('/').pop()
    let takeawayData: any = await this.app.service('session-takeaway-snapshot').Model.findOne({_id: takeawayId})
    let session: any = await this.app.service('session').Model.findOne({_id: takeawayData.session})
    if (session?.booking) {
      await this.app.service('service-booking').Model.updateOne(
        {_id: session.booking},
        {
          $unset: {session: 1, serviceAuthId: 1},
        }
      )
    }
    if (session) {
      await this.app.service('session').remove(session._id)
    }
    return await this.Model.updateOne(
      {_id: id},
      {
        status: 1,
        interviewInvited: false,
        interviewApply: false,
        $unset: {interviewPack: '', takeaway: '', takeawayId: '', takeawayCreatedAt: ''},
      }
    )
  }
}

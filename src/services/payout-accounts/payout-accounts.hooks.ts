import * as authentication from '@feathersjs/authentication'
import hook from '../../hook'
import {accessControl} from '../../hooks/accessControl'
// Don't remove this comment. It's needed to format import lines nicely.

const {authenticate} = authentication.hooks

export default {
  before: {
    all: [
      authenticate('jwt'),
      accessControl({
        schoolAccess: ['mainContact'],
        managerRoles: ['admin', 'accountant'],
      }),
    ],
    find: [],
    get: [],
    create: [hook.disallowExternal],
    update: [hook.disallowExternal],
    patch: [hook.disallowExternal],
    remove: [hook.disallowExternal],
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}

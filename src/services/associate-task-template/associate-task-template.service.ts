import {ServiceAddons} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import {AssociateTaskTemplate} from './associate-task-template.class'
import createModel from '../../models/associate-task-template.model'
import hooks from './associate-task-template.hooks'

declare module '../../declarations' {
  interface ServiceTypes {
    'associate-task-template': AssociateTaskTemplate & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    whitelist: ['$exists', '$regex', '$options'],
    paginate: app.get('paginate'),
    _id: '_id',
    multi: ['remove'],
  }
  app.use('/associate-task-template', new AssociateTaskTemplate(options, app))
  const service = app.service('associate-task-template')

  service.hooks(hooks)
}

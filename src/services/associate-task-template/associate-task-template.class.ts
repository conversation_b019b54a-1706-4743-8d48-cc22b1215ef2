import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'

export class AssociateTaskTemplate extends Service {
  app: Application
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async create(data: any) {
    const createdTask = await this.Model.create(data)
    if (data?.pid) {
      const essayId = data.pid
      const taskId = createdTask._id
      try {
        await this.app.service('essay-template').Model.findByIdAndUpdate(essayId, {$addToSet: {associatedTasks: taskId}}, {new: true})
      } catch (err) {
        console.error(err)
      }
    }
    return createdTask
  }

  async getFilled(params?: Params) {
    const result = await super.find(params)
    const data = Array.isArray(result) ? result : result.data
    const updatedData = await Promise.all(
      data.map(async (doc: any) => {
        const {sections = [], priceSettings = {}} = doc
        let setPriceFlag = false
        for (const qualification of Object.keys(priceSettings)) {
          const q = priceSettings[qualification]
          if (!q?.sections) continue

          const sectionIdsInQualification = q.sections.map((s: any) => String(s.sectionId))

          const hasAll = sections.every((sId: string) => sectionIdsInQualification.includes(String(sId)))

          if (hasAll) {
            setPriceFlag = true
            break
          }
        }
        const nameOk = typeof doc.name === 'string' && doc.name.trim().length > 0
        const coverOk = typeof doc.cover === 'string' && doc.cover.trim().length > 0
        const verificationOk = Array.isArray(doc.verificationItems) && doc.verificationItems.length > 0
        const filled = setPriceFlag && nameOk && coverOk && verificationOk
        await this.Model.findByIdAndUpdate(doc._id, {
          $set: {setPriceFlag, filled},
        })
        return {
          ...(doc.toObject?.() || doc),
          setPriceFlag,
          filled,
        }
      })
    )
    return Array.isArray(result) ? updatedData : {...result, data: updatedData}
  }

  async find(params?: Params): Promise<any> {
    return this.getFilled(params)
  }

  async get(id: string, params?: Params): Promise<any> {
    const filled = await this.getFilled({
      ...params,
      query: {...(params?.query || {}), _id: id},
    })
    if (Array.isArray(filled)) {
      return filled[0] || null
    }
    return filled.data[0] || null
  }

  async patch(id: any, data: any, params?: any) {
    if (data.sections && Array.isArray(data.sections)) {
      for (const sectionUpdate of data.sections) {
        const sectionId = sectionUpdate._id
        const templateSection = await this.app.service('template-section').get(sectionId)
        templateSection.feedback = templateSection.feedback || {}
        templateSection.todoList = templateSection.todoList || {}
        templateSection.rubrics = templateSection.rubrics || {}
        if (sectionUpdate.feedback !== undefined) {
          templateSection.feedback[id] = sectionUpdate.feedback
        }
        if (sectionUpdate.todoList !== undefined) {
          templateSection.todoList[id] = sectionUpdate.todoList
        }
        if (sectionUpdate.rubrics !== undefined) {
          templateSection.rubrics[id] = {status: sectionUpdate.rubrics}
        }
        await this.app.service('template-section').patch(sectionId, {
          feedback: templateSection.feedback,
          todoList: templateSection.todoList,
          rubrics: templateSection.rubrics,
        })
      }
    }
    return super.patch(id, data, params)
  }

  async patchImportSections(data: {atid: string; sections: string[]}) {
    const {atid, sections} = data
    if (!atid || !sections?.length) {
      throw new Error('atid and sections[] are required')
    }
    const templateSectionService = this.app.service('template-section')
    await templateSectionService.Model.updateMany({_id: {$in: sections}}, {$addToSet: {atid}})
    await this.Model.updateOne({_id: atid}, {$addToSet: {sections: {$each: sections}}})
    const updatedSections = await templateSectionService.find({
      query: {_id: {$in: sections}},
      paginate: false,
    })
    const updatedAT = await this.get(atid)

    return {
      success: true,
      sections: updatedSections,
      at: updatedAT,
    }
  }
}

import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {HookContext} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import {commitTransactionSession, rollbackTransactionSession, startTransactionSession, TxnParams} from '../../hooks/dbTransactions'
import {calcDurationMin, calcHourlyRate, campusPrice, getRateConfig} from './libs/rateCalculations'
import {notesMap} from './libs/utils'
import {shouldExecuteInInterval} from '../../hooks/cronScheduler'

export class IncomeLog extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  /** Create new income log */
  async createIncomeLog(data: any, params?: TxnParams): Promise<any> {
    const {uid, isSchool, tab, category, setting_category, status, isParent, parentId, businessId, eventDetails, notes, amount}: any = data

    if (!Acan.isValidNumber(amount)) return
    let value = Number(amount)
    console.log('createIncomeLog', value)
    if (setting_category) {
      const incomeSetting = await this.app.service('income-setting').calcIncome({category: setting_category, amount})
      console.log('incomeSetting', incomeSetting)
      if (incomeSetting.income === 0) return
      value = incomeSetting.income
    }
    value = Math.floor(value)
    console.log('Income_value_0', status, value)
    if (status === 0 && value === 0) return
    console.log('Income_value_1', status, value)

    let updatedBalance: any = {}
    let actualAt: any
    if (status === 1) {
      actualAt = new Date()
    } else {
      updatedBalance = await this.app.service('wallet-balance').getBalance({uid, balanceType: 'income'}, {})
    }

    let expected = undefined
    if (status === 0) expected = value

    const options = Acan.getTxnOptions(params)
    return await this.Model.findOneAndUpdate(
      {
        uid: uid,
        businessId: businessId,
        category: category,
      },
      {
        $set: {
          isSchool,
          tab,
          category,
          setting_category,
          value,
          total: updatedBalance.availableBalance ?? 0,
          expected,
          actualAt,
          status,
          isParent,
          parentId,
          businessId,
          eventDetails,
          notes,
          needsWalletSync: status === 1 && value !== 0 ? true : undefined,
        },
      },
      {new: true, upsert: true, ...options}
    )
  }
  /** Update existing income log. */
  async updateIncomeLog(
    query: {_id?: string; uid?: string; isSchool?: boolean; businessId?: string; category?: string; status?: number; isParent?: any},
    data: any,
    params?: TxnParams
  ) {
    console.log('data.$set.value', data.$set.value)
    const options = Acan.getTxnOptions(params)
    const patchData = {
      $set: {
        ...(data.$set || {}),
      },
    }
    if (data.$set?.status === 1) {
      patchData.$set.actualAt = new Date()
      patchData.$set.needsWalletSync = data.$set.value !== 0 ? true : undefined
    }
    console.log('query', JSON.stringify(query))
    console.log('patchData', JSON.stringify(patchData))
    return await this.Model.findOneAndUpdate(query, patchData, options)
  }

  /**
   * Primary function for income generation across the app.
   * This should be used by all other services to generate income.
   */
  async generateIncome(data: any) {
    let start = Date.now()
    console.log('generateIncome_START', data.source, new Date())
    switch (data.source) {
      case 'order': {
        await this.app.service('income-log').orderIncome(data)
        break
      }
      case 'booking_created': {
        await this.app.service('income-log').tchServExpIncome(data)
        break
      }
      case 'booking_confirmed': {
        await this.app.service('income-log').tchIncOnConfirm(data)
        break
      }
      case 'booking_cancelled': {
        await this.app.service('income-log').tchIncOnCancel(data)
        break
      }
      case 'section-tracking': {
        await this.app.service('income-log').serviceTaskActIncome(data)
        break
      }
      case 'workshop': {
        await this.app.service('income-log').workshopActIncome(data)
        break
      }
      default: {
        break
      }
    }
    console.log('generateIncome_END', new Date().toLocaleTimeString(), Date.now() - start)
  }

  /**
   * Income for order related items. Includes:
   * - Non-refundable items: {@link nonRefundItemIncome}
   * - Workshop expected income: {@link workshopExpIncome}
   * - Workshop cancellation income: {@link workshopIncomeOnCancel}
   */
  async orderIncome(job: any, params?: TxnParams) {
    const order = job.snapshot
    console.log('orderIncome', order.type, order.price)
    if (!order || !order.price || order.price <= 0) {
      return
    }

    const eligibleOrderTypes = ['unit', 'session_self_study', 'premium_cloud', 'prompt', 'session_public']
    if (!eligibleOrderTypes.includes(order.type)) {
      return
    }

    if (order.type === 'session_public' && job.logStatus === 1) {
      await this.workshopIncomeOnCancel(order, job.otherDetails?.childSessions, this.app, params)
      return
    }

    const styleCategoryMapping: {[key: string]: string} = {
      unit: 'library_content',
      premium_cloud: 'premium_contents_task_sessions',
      prompt: 'new_prompt_content',
    }

    const eligibleLinks = []
    for (const link of order.links || []) {
      // const remainingPrice = Number((link.price - link.refundPrice).toFixed(0))
      // if (remainingPrice <= 0) {
      //   continue
      // }

      if (!link.goods?.uid) continue

      let isEligible = false
      let category = null

      if (['premium_cloud', 'unit', 'prompt'].includes(link.style)) {
        isEligible = true
        category = styleCategoryMapping[link.style]
      } else if (order.type === 'session_self_study' && link.style === 'session') {
        isEligible = true
        category = 'self_study_content'
      } else if (order.type === 'session_public' && link.style === 'session') {
        isEligible = true
        category = 'teaching_service'
      }

      if (isEligible && category) {
        eligibleLinks.push({
          link,
          category,
          setting_category: category,
        })
      }
    }

    if (eligibleLinks.length === 0) {
      return
    }
    console.log('eligibleLinks:', eligibleLinks.length)

    for (const eligibleLink of eligibleLinks) {
      switch (eligibleLink.category) {
        case 'teaching_service': {
          await this.workshopExpIncome(eligibleLink, order, params)
          break
        }
        default: {
          await this.nonRefundItemIncome(eligibleLink, order, params)
        }
      }
    }
  }
  /**
   * Actual income for Non-refundable items from order
   */
  async nonRefundItemIncome(eligibleLink: {link: any; category: string; setting_category: string}, order: any, params?: TxnParams) {
    const {link, category, setting_category} = eligibleLink
    const incomeLogData = {
      uid: link.goods.uid,
      isSchool: false,
      tab: 'earn',
      category,
      setting_category,
      status: 1,
      businessId: `${link.id.toString()}-${order._id.toString()}`,
      eventDetails: {
        id: link.id,
        name: link.name,
        cover: link.cover,
        style: link.style,
        orderId: order._id.toString(),
        orderType: order.type,
      },
      amount: link.price || 0,
    }

    const incomePromises = [this.app.service('income-log').createIncomeLog(incomeLogData, params)]

    if (link.style === 'premium_cloud' && link.goods.approval?.approver) {
      const auditorIncomeLogData = {
        ...incomeLogData,
        uid: link.goods.approval.approver,
        category: 'premium_content_audit',
        setting_category: 'premium_content_audit',
      }
      incomePromises.push(this.app.service('income-log').createIncomeLog(auditorIncomeLogData, params))
    }

    await Promise.all(incomePromises)
  }

  /**
   * Associated Task/Service Task Actual Income
   */
  async serviceTaskActIncome(job: any, params?: TxnParams) {
    const results = Array.isArray(job.snapshot) ? job.snapshot : [job.snapshot]
    if (!results || results.length === 0 || !results[0]) return
    for (const result of results) {
      await this.app.service('income-log').createIncomeLog(
        {
          uid: result.servicer,
          isSchool: false,
          tab: 'earn',
          category: 'associated_task',
          status: 1,
          businessId: result.sectionId,
          eventDetails: {
            id: result.sectionId,
            name: result.sectionSnapshot?.name,
            cover: result.sectionSnapshot?.taskDetails?.cover,
            sessionStatus: result.status,
          },
          amount: result.creditedPoints * 100,
        },
        params
      )
    }
  }

  /**
   * Workshop expected income.
   * Calculated during order creation
   */
  async workshopExpIncome(eligibleLink: {link: any; category: string; setting_category: string}, order: any, params?: TxnParams) {
    console.log('workshopExpIncome', eligibleLink.category)
    const {link, category} = eligibleLink
    const {goods} = link
    // todo
    // recheck the logic for expected log creation
    if (!goods || !goods.uid) {
      console.log(`Skipping workshop income log - no goods or uid for link ${link.id}`)
      return
    }
    const childSessions = goods.childs?.length ? goods.childs.filter((child: any) => child._id && child.sessionType === 'live') : []
    const setting_category = goods.authId ? 'teaching_service_classcipe' : 'teaching_service_own'

    // Create parent income log for the main workshop
    const parentIncomeLogData = {
      uid: goods.uid,
      isSchool: false,
      tab: 'earn',
      category,
      setting_category,
      status: 0,
      isParent: childSessions.length > 0 ? true : undefined,
      businessId: `${goods._id.toString()}-${order._id.toString()}`,
      eventDetails: {
        id: goods._id.toString(),
        name: goods.name,
        cover: goods.image,
        orderId: order._id.toString(),
        orderType: order.type,
        sessionStatus: 'scheduled',
        sessionType: goods.type,
        salesPrice: link.price,
      },
      amount: link.price || 0,
    }
    console.log('setting_category', setting_category)
    console.log('isParent', parentIncomeLogData.isParent)
    // Insert primary income log
    // console.log('parentIncomeLogData', JSON.stringify(parentIncomeLogData))
    const parentLog = await this.app.service('income-log').createIncomeLog(parentIncomeLogData, params)
    console.log('Created parent income log -', parentLog?._id)
    // If this is a parent type and has children, process child sessions
    if (parentLog?._id && childSessions.length > 0) {
      const childDocs = await this.app
        .service('session')
        .Model.find({
          _id: {$in: childSessions.map((v: any) => v._id)},
        })
        .select(['_id', 'name', 'image'])
        .lean()

      const childInfoMap: any = {}
      for (const childDoc of childDocs) {
        childInfoMap[childDoc._id.toString()] = childDoc
      }

      const unitSessionPrice = link.price / childSessions.length
      for (const childSession of childSessions) {
        const sessionId = childSession._id.toString()
        console.log('childSession', sessionId, childInfoMap[sessionId])
        const childIncomeLogData = {
          uid: goods.uid,
          isSchool: false,
          tab: 'earn',
          category,
          setting_category,
          status: 0,
          isParent: false,
          parentId: parentLog._id?.toString(),
          businessId: `${sessionId}-${order._id.toString()}`,
          eventDetails: {
            id: sessionId,
            name: childInfoMap[sessionId]?.name,
            cover: childInfoMap[sessionId]?.image,
            orderId: order._id.toString(),
            orderType: order.type,
            sessionStatus: 'scheduled',
            sessionType: childSession.sessionType,
            salesPrice: unitSessionPrice,
          },
          amount: unitSessionPrice,
        }

        await this.app.service('income-log').createIncomeLog(childIncomeLogData, params)
        console.log(`Created child session income log - session: ${sessionId}`)
      }
    }
  }

  /**
   * Workshop actual income after session ends
   */
  async workshopActIncome(job: any, params?: TxnParams) {
    const log: any = await this.updateIncomeLog(job.otherDetails.query, job.otherDetails.payload, params)
    if (!log) return

    const options = Acan.getTxnOptions(params)

    let parentLogValue: number = 0
    let isRemainingChild: boolean = false

    const childLogs: any = await this.app
      .service('income-log')
      .Model.find(
        {
          parentId: log.parentId,
        },
        null,
        options
      )
      .select(['value', 'status'])
      .lean()

    for (const child of childLogs) {
      parentLogValue += child.value
      if (child.status === 0) {
        isRemainingChild = true
      }
    }
    await this.updateIncomeLog(
      {_id: log.parentId},
      isRemainingChild ? {$set: {value: parentLogValue}} : {$set: {status: 1, 'eventDetails.sessionStatus': 'ended', value: parentLogValue}},
      params
    )

    if (log.value !== log.expected || !log.eventDetails?.salesPrice) return

    await this.contentContributorIncome({authId: job.snapshot.authId, salesPrice: log.eventDetails.salesPrice, relatedLogId: log._id.toString()}, params)
  }

  /**
   * Workshop actual on order/session cancellation
   */
  async workshopIncomeOnCancel(order: any, childSessions: any[], app: Application, params?: TxnParams) {
    if (order.type !== 'session_public') {
      return
    }

    const sessionLink = order.links.find((item: any) => item.style === 'session' && item.pending === true)
    if (!sessionLink) {
      return
    }

    if (childSessions && childSessions.length > 0) {
      await this.cancelWorkshopChild(order, sessionLink, childSessions, params)
    } else {
      await this.cancelWorkshopFull(order, sessionLink, params)
    }
  }

  /**
   * Used in {@link workshopIncomeOnCancel}
   */
  async cancelWorkshopChild(order: any, link: any, childSessions: any[], params?: TxnParams) {
    const options = Acan.getTxnOptions(params)
    for (const childSession of childSessions) {
      await this.app.service('income-log').updateIncomeLog(
        {
          uid: link.goods.uid,
          businessId: `${childSession._id.toString()}-${order._id.toString()}`,
          category: 'teaching_service',
          status: 0,
        },
        {
          $set: {
            value: 0,
            status: 1,
            notes: notesMap[order.status],
            'eventDetails.sessionStatus': 'cancelled',
          },
        },
        params
      )
    }

    // Update parent log based on remaining child sessions
    const liveSessions = link.goods?.childs?.length ? link.goods.childs.filter((v: any) => v.sessionType === 'live') : []
    const isFullyCancelled = link.removed === true || link.refundedItems.length >= liveSessions.length
    console.log('isFullyCancelled', isFullyCancelled, link.refundedItems.length, liveSessions.length)
    let parentLogValue: number = 0
    const childLogs = await this.app
      .service('income-log')
      .Model.find(
        {
          uid: link.goods.uid,
          businessId: {$in: liveSessions.map((v: any) => `${v._id.toString()}-${order._id.toString()}`)},
          category: 'teaching_service',
        },
        null,
        options
      )
      .select('value')
      .lean()
    console.log('childLogs', childLogs.length)
    parentLogValue = childLogs.reduce((prev: any, cur: any) => {
      return prev + cur.value
    }, 0)
    console.log('parentLogValue', parentLogValue)
    await this.app.service('income-log').updateIncomeLog(
      {
        uid: link.goods.uid,
        businessId: `${link.goods._id.toString()}-${order._id.toString()}`,
        category: 'teaching_service',
        status: 0,
      },
      {
        $set: {
          status: isFullyCancelled ? 1 : 0,
          notes: isFullyCancelled ? notesMap[order.status] : '',
          value: parentLogValue,
          'eventDetails.sessionStatus': isFullyCancelled
            ? link.refundedItems.length === liveSessions.length
              ? 'cancelled'
              : 'partially_cancelled'
            : 'ongoing',
        },
      },
      params
    )
  }

  /**
   * Used in {@link workshopIncomeOnCancel}
   */
  async cancelWorkshopFull(order: any, link: any, params?: TxnParams) {
    const options = Acan.getTxnOptions(params)
    console.log('link.removed', link.removed)
    if (link.removed !== true) {
      return
    }

    const alreadyCancelled = link.refundedItems?.map((v: any) => v._id)
    const allLiveSessions: any = []
    const sessionsToCancel: any = []

    if (link.goods?.childs?.length) {
      link.goods.childs.forEach((v: any) => {
        if (v.sessionType === 'live') {
          allLiveSessions.push(v)
        }
        if (v.sessionType === 'live' && !alreadyCancelled.includes(v._id)) {
          sessionsToCancel.push(v)
        }
      })
    }
    console.log('sessionsToCancel', sessionsToCancel.length, allLiveSessions.length)
    if (sessionsToCancel.length > 0) {
      // Update all child session logs with status 0
      for (const childSession of sessionsToCancel) {
        console.log('childSession', childSession._id.toString())
        await this.app.service('income-log').updateIncomeLog(
          {
            uid: link.goods.uid,
            businessId: `${childSession._id.toString()}-${order._id.toString()}`,
            category: 'teaching_service',
            status: 0,
          },
          {
            $set: {
              value: 0,
              status: 1,
              notes: notesMap[order.status],
              'eventDetails.sessionStatus': 'cancelled',
            },
          },
          params
        )
      }
    }

    let parentLogValue: number = 0
    if (allLiveSessions.length) {
      const childLogs = await this.app
        .service('income-log')
        .Model.find(
          {
            uid: link.goods.uid,
            businessId: {$in: allLiveSessions.map((v: any) => `${v._id.toString()}-${order._id.toString()}`)},
            category: 'teaching_service',
          },
          null,
          options
        )
        .select('value')
        .lean()
      console.log('childLogs', childLogs.length)
      parentLogValue = childLogs.reduce((prev: any, cur: any) => {
        return prev + cur.value
      }, 0)
      console.log('parentLogValue', parentLogValue)
    }

    await this.app.service('income-log').updateIncomeLog(
      {
        uid: link.goods.uid,
        businessId: `${link.goods._id.toString()}-${order._id.toString()}`,
        category: 'teaching_service',
        status: 0,
      },
      {
        $set: {
          value: parentLogValue,
          status: 1,
          notes: notesMap[order.status],
          'eventDetails.sessionStatus': 'cancelled',
        },
      },
      params
    )
  }

  /**
   * Teaching Service Expected Income.
   * For service package related services.
   * For workshop see {@link workshopExpIncome}.
   */
  async tchServExpIncome({snapshot: d}: {snapshot: HookContext}) {
    console.log('tchServExpIncome', d.result._id)
    const results = []
    if (d.result && Array.isArray(d.result) && d.result.length > 0) {
      d.result.forEach((item) => {
        if (item.type !== 'serviceTask') {
          results.push(item)
        }
      })
    } else if (d.result.type !== 'serviceTask') {
      results.push(d.result)
    }
    if (!results.length) return d

    for (const result of results) {
      if (result.type === 'content' && !result.session) {
        const res: any = await this.app.service('session').Model.findOne({booking: result._id.toString()}).select(['name', 'image'])
        result.session = res
      }
      const income = await this.app.service('income-log').calcTeachingServiceIncome({bookingDoc: result})
      console.log('income', JSON.stringify(income))
      result.expectedIncome = income
    }

    for (const result of results) {
      await this.app.service('income-log').createIncomeLog({
        uid: result.servicer,
        isSchool: false,
        tab: 'earn',
        category: 'teaching_service',
        status: 0,
        businessId: result._id.toString(),
        eventDetails: {
          id: result._id.toString(),
          name: result.name || result.session?.name,
          cover: result.session?.image,
          incomeBreakup: result.expectedIncome,
          sessionStatus: result.session ? 'scheduled' : 'booked',
          session: result.session?._id.toString(),
          booking: {
            _id: result._id.toString(),
            name: result.name,
            start: result.start,
            end: result.end,
            servicer: result.servicer,
            booker: result.booker,
          },
          salesPrice: result.expectedIncome.salesPrice,
        },
        amount: (result.expectedIncome.teachingService + result.expectedIncome.travel) * 100,
      })
    }
  }

  /**
   * Teaching Service Income on cancellation
   */
  async tchIncOnCancel({bookingId, bookingDoc, sessionId, sessionDoc}: any, params?: TxnParams) {
    // - Cancelled by booker- before 2hrs: 0
    // - Cancelled by booker - within 2hrs: -20% of expected income
    // - Cancelled by servicer:
    // - 2 hrs before start: 0
    // - 2 hrs after start: -30% of expected income(penalty)
    // - timeout: -20% of expected income(penalty)
    console.log('tchIncOnCancel')
    let uid = ''
    let cancelledBy = ''
    let cancelledAt = ''
    let sessionStartAt = ''
    let businessId = ''
    let booking
    if (bookingId || bookingDoc) {
      booking = bookingDoc || (await this.app.service('service-booking').Model.findById(bookingId))
      if (!booking || booking?.type === 'serviceTask' || !booking.cancel) return
      uid = booking.servicer
      cancelledBy = booking.cancel
      cancelledAt = booking.canceledAt
      sessionStartAt = booking.start
      businessId = bookingId
    } else if (sessionId || sessionDoc) {
      const session: any = sessionDoc || (await this.app.service('session').Model.findById(sessionId))
    } else return

    const expectedIncomeLog: any = await this.app.service('income-log').Model.findOne({
      uid,
      businessId,
      category: 'teaching_service',
    })
    if (!expectedIncomeLog) return

    let patchData: any
    const cancelledBefore2Hrs = new Date(sessionStartAt).getTime() - new Date(cancelledAt || 0).getTime() > 2 * 60 * 60 * 1000
    if (cancelledBy === 'booker' && cancelledBefore2Hrs) {
      patchData = {
        value: 0,
        notes: 'Amount change due to user cancellation',
        status: 1,
        eventDetails: {
          ...(expectedIncomeLog.eventDetails || {}),
          sessionStatus: 'cancelled',
          cancelledBy: 'booker',
          cancelledAt,
        },
      }
    } else if (cancelledBy === 'booker' && !cancelledBefore2Hrs) {
      const value = Math.floor(expectedIncomeLog.value * 0.2)
      patchData = {
        value,
        notes: 'Amount change due to user refund',
        status: 1,
        eventDetails: {
          ...(expectedIncomeLog.eventDetails || {}),
          sessionStatus: 'cancelled',
          cancelledBy: 'booker',
          cancelledAt,
        },
      }
    } else if (cancelledBy === 'servicer' && cancelledBefore2Hrs) {
      patchData = {
        value: 0,
        notes: ' Amount change due to user refund (cancelled by facilitator)',
        status: 1,
        eventDetails: {
          ...(expectedIncomeLog.eventDetails || {}),
          sessionStatus: 'cancelled',
          cancelledBy: 'servicer',
          cancelledAt,
        },
      }
    } else if (cancelledBy === 'servicer' && !cancelledBefore2Hrs) {
      const value = -1 * Math.floor(expectedIncomeLog.value * 0.3)
      patchData = {
        value,
        notes: 'Amount change due to user refund (cancelled by facilitator)',
        status: 1,
        eventDetails: {
          ...(expectedIncomeLog.eventDetails || {}),
          sessionStatus: 'cancelled',
          cancelledBy: 'servicer',
          cancelledAt,
        },
      }
    } else if (cancelledBy === 'timeout') {
      const value = -1 * Math.floor(expectedIncomeLog.value * 0.2)
      patchData = {
        value,
        notes: 'Amount change due to penalty',
        status: 1,
        eventDetails: {
          ...(expectedIncomeLog.eventDetails || {}),
          sessionStatus: 'cancelled',
          cancelledBy: 'timeout',
          cancelledAt,
        },
      }
    } else return
    console.log('cancelledBefore2Hrs', cancelledBefore2Hrs, cancelledBy)
    console.log('expectedIncomeLog._id', expectedIncomeLog._id)
    console.log('patchData', JSON.stringify(patchData))
    await this.app.service('income-log').updateIncomeLog(
      {
        _id: expectedIncomeLog._id.toString(),
      },
      {$set: patchData}
    )
  }

  /**
   * Teaching Service Income after session ends. If:
   * 1. no pending teaching accident
   * 2. takeaway is sent to the booker
   */
  async tchIncOnConfirm(job: any, params?: TxnParams) {
    const log: any = await this.updateIncomeLog(job.otherDetails.query, job.otherDetails.payload, params)
    if (!log || log.value !== log.expected || !log.eventDetails?.salesPrice) return

    // content contributor income
    let packUserDoc: any = null
    if (job.snapshot.type === 'content') {
      packUserDoc = await this.app.service('service-pack-user').Model.findById(job.snapshot.packUser)
    }

    if (packUserDoc?.snapshot?.type === 'content' && packUserDoc.snapshot.uid) {
      await this.contentContributorIncome({authDoc: packUserDoc.snapshot, salesPrice: log.eventDetails.salesPrice, relatedLogId: log._id.toString()}, params)
    }
  }

  /**
   * Teaching Service Hourly Amount Calculation.
   * Used in {@link tchServExpIncome}
   * */
  async calcTeachingServiceIncome({
    bookingId,
    bookingDoc,
    sessionId,
    sessionDoc,
  }: any): Promise<{teachingService: number; travel: number; salesPrice?: number | undefined}> {
    const result = {teachingService: 0, travel: 0}
    let packUserId: string = ''
    let duration = 0
    let bookingTimes = 0
    if (bookingId || bookingDoc) {
      const booking: any = bookingDoc || (await this.app.service('service-booking').Model.findById(bookingId))
      if (!booking || booking?.type === 'serviceTask' || !booking.packUser || booking.cancel) return result
      packUserId = booking.packUser
      duration = booking.duration * booking.times
      bookingTimes = booking.times
    } else if (sessionId || sessionDoc) {
      const session: any = sessionDoc || (await this.app.service('session').Model.findById(sessionId))
      if (!session || !session.packUser) return result
      packUserId = session.packUser
      duration = calcDurationMin({start: session.start, end: session.end})
    } else return result
    console.log('packUserId', packUserId)
    console.log('duration', duration)
    if (!Acan.isValidNumber(duration) || duration <= 0) return result

    let servicePackage: any = {}
    const packUser: any = await this.app.service('service-pack-user').Model.findById(packUserId)
    if (!packUser || !packUser.snapshot) return result

    servicePackage = packUser.snapshot

    const isContentOrientated = packUser.snapshot.type === 'content' && packUser.pid
    console.log('isContentOrientated', isContentOrientated)

    let salesPrice: number | undefined
    if (packUser.snapshot.type === 'content' && packUser.pid) {
      const promises = [this.app.service('service-pack-user').Model.findById(packUser.pid)]
      if (packUser.payMethod === 'cash' && packUser.order) {
        promises.push(this.app.service('order').Model.findById(packUser.order))
      }
      const [parentPackUser, order]: any[] = await Promise.all(promises)

      if (!parentPackUser || !parentPackUser.snapshot) return result
      servicePackage = parentPackUser.snapshot

      if (order) {
        const product = order.links.find((item: any) => item.id === packUser.snapshot._id)
        salesPrice = product?.price ? product.price / (product.count || 1) : undefined
      }
    }

    if (servicePackage.break && servicePackage.break > 0 && bookingTimes) {
      duration = duration - servicePackage.break * bookingTimes
    }
    console.log('duration_1', duration, servicePackage.break, bookingTimes)
    console.log('serviceRoles', servicePackage.serviceRoles)
    console.log('mentoringType', servicePackage.mentoringType)
    if (!['mentoring', 'substitute', 'consultant'].includes(servicePackage.serviceRoles)) return result

    const {config, sysSubjectData} = await getRateConfig(servicePackage.mentoringType, this.app)
    console.log('config', !!config, !!sysSubjectData)

    const commonConfig = {
      mentoringType: servicePackage.mentoringType,
      qualification: servicePackage.qualification,
      gradeGroup: servicePackage.gradeGroup,
      config,
      sysSubjectData,
    }
    console.log('qualification', commonConfig.qualification)
    console.log('curriculum', servicePackage.curriculum)
    console.log('gradeGroup', servicePackage.gradeGroup)
    console.log('sysSubjectData', JSON.stringify(sysSubjectData))
    const hourRateList: number[] = []
    // console.log('rate_config', JSON.stringify(config))
    if (servicePackage.mentoringType === 'academic') {
      const subjects: string[] = isContentOrientated ? [packUser.snapshot.subject] : servicePackage.subject || []

      subjects.forEach((subject: string) => {
        console.log('subject', subject)
        const rate = calcHourlyRate({
          ...commonConfig,
          curriculum: servicePackage.curriculum,
          subject: subject,
        })
        console.log('rate', rate)
        hourRateList.push(rate)
      })
    } else {
      const topics = packUser.snapshot?.topic || []

      topics.forEach((topic: any) => {
        console.log('topic', topic)
        if (typeof topic === 'string') return
        const rate = calcHourlyRate({
          ...commonConfig,
          topicId: topic._id?.toString(),
        })
        console.log('rate', rate)
        hourRateList.push(rate)
      })
    }
    console.log('hourRateList', hourRateList)
    if (hourRateList.length === 0) return result

    const teachingServiceAmount = Math.max(...hourRateList) * (duration / 60)
    console.log('teachingServiceAmount', teachingServiceAmount)
    console.log('isOnCampus', servicePackage.isOnCampus)
    const travelAmount = await campusPrice(servicePackage, this.app)
    console.log('travelAmount', travelAmount)

    return {
      teachingService: Number(teachingServiceAmount.toFixed(2)),
      travel: Number(travelAmount.toFixed(2)),
      salesPrice,
    }
  }

  /**
   * Add income for content contributor & author
   * of premium content used in lecture session or premium workshops
   */
  async contentContributorIncome(
    {authId, authDoc, salesPrice, relatedLogId}: {authId?: string; authDoc?: any; salesPrice: number; relatedLogId: string},
    params?: TxnParams
  ) {
    // content contributor income
    const auth: any = authDoc ? authDoc : await this.app.service('service-auth').Model.findById(authId)
    if (!auth) return
    const promises = []
    const payload = {
      amount: salesPrice,
      businessId: `${auth._id.toString()}-${relatedLogId}`,
      status: 1,
      isSchool: false,
      eventDetails: {
        id: auth._id.toString(),
        name: auth.unitSnapshot?.name,
        cover: auth.unitSnapshot?.cover,
      },
    }
    // for author
    if (auth?.uid) {
      promises.push(
        this.createIncomeLog(
          {
            ...payload,
            uid: auth.uid,
            category: 'premium_contents_unit_module',
            setting_category: 'premium_contents_unit_module',
          },
          params
        )
      )
    }
    // for approver
    if (auth?.approval?.approver) {
      promises.push(
        this.createIncomeLog(
          {
            ...payload,
            uid: auth.approval.approver,
            category: 'premium_content_audit',
            setting_category: 'premium_content_audit',
          },
          params
        )
      )
    }
    if (promises.length > 0) {
      await Promise.all(promises)
    }
  }

  /**
   * Income wallet balance synchronization.
   */
  async syncWalletBalance() {
    // execute every 15 minutes
    if (!shouldExecuteInInterval(15)) return

    const logs: any = await this.Model.find({needsWalletSync: true, status: 1})
      .sort({actualAt: 1})
      .select('uid isSchool value')
      .limit(100) // fetch 100 records at a time, to prevent overloading
      .lean()

    const batchSize = 20
    for (let i = 0; i < logs.length; i += batchSize) {
      const batch = logs.slice(i, i + batchSize)

      const session = await startTransactionSession(this.app)
      const transactionParams: TxnParams = {mongoose: {session}, sideEffectsToExecute: []}

      try {
        for (const log of batch) {
          let updatedBalance: any = {}
          if (!log.isParent) {
            updatedBalance = await this.app
              .service('wallet-balance')
              .updateBalance(
                {uid: log.uid, isSchool: log.isSchool, balanceType: 'income', amount: log.value, tab: log.tab, transactionId: log._id.toString()},
                transactionParams
              )
          } else {
            updatedBalance = await this.app.service('wallet-balance').getBalance({uid: log.uid, balanceType: 'income'}, transactionParams)
          }

          await this.app.service('income-log').Model.updateOne(
            {_id: log._id},
            {
              $set: {
                total: updatedBalance.availableBalance ?? 0,
              },
              $unset: {needsWalletSync: ''},
            },
            {session}
          )
        }

        await commitTransactionSession(session, this.app, transactionParams)
      } catch (error) {
        await rollbackTransactionSession(session)
      }
    }
  }
}

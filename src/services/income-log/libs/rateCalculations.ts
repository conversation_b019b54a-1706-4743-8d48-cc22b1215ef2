import {Application} from '../../../declarations'
import {sortBySysCurriculum} from '../../service-auth/unit/dict'

export async function campusPrice(servicePackage: any, app: Application) {
  if (!servicePackage.isOnCampus || !servicePackage.country || !servicePackage.city) return 0
  const campuses = (await app.service('campus-location').Model.find({country: servicePackage.country, city: servicePackage.city})) as {
    compensationHour: number
    experienceRate: any
    tutorRate: any
  }[]
  const currentCampus = campuses[0]
  if (!currentCampus) return 0
  let hourlyRate = 0
  const {gradeGroup = []} = servicePackage
  const totalHour = currentCampus?.compensationHour
  const currentCampusHourRate = servicePackage?.qualification === 'experiencedTeacher' ? currentCampus?.experienceRate : currentCampus?.tutorRate
  hourlyRate = currentCampusHourRate[gradeGroup?.[0]] || 0
  const totalPrice = (totalHour * hourlyRate).toFixed(2)

  return parseFloat(totalPrice)
}

export async function getRateConfig(mentoringType: string, app: Application) {
  const serviceKey = `Service:${'mentoring'}:${[mentoringType]}`
  const config: any = await app.service('conf').Model.findById(serviceKey)
  if (config?.val?.curriculum?.length) {
    config.val.curriculum = sortBySysCurriculum(config.val.curriculum)
  }
  let sysSubjectData: any
  if (['essay', 'teacherTrainingSubject'].includes(mentoringType)) {
    sysSubjectData = await app.service('subjects').Model.findOne({uid: '1', subjectCode: mentoringType, curriculum: 'pd'})
  }
  return {config: config?.val, sysSubjectData}
}

export function calcHourlyRate({mentoringType, qualification, curriculum, subject, topicId, gradeGroup, config, sysSubjectData}: any): number {
  let result = 0
  switch (mentoringType) {
    case 'academic': {
      result = getCurriculumHourRate({
        qualification,
        curriculum,
        config,
        subject,
        gradeGroup,
      })
      break
    }

    case 'essay':
    case 'teacherTrainingSubject': {
      if (!sysSubjectData) return 0

      let topicFirstLayerId: any
      sysSubjectData?.['topic']?.some((firstLayer: any) => {
        console.log('firstLayer', firstLayer._id?.toString())
        firstLayer.child.some((secondLayer: any) => {
          console.log('secondLayer', secondLayer._id?.toString())
          if (secondLayer._id?.toString() === topicId) {
            topicFirstLayerId = firstLayer._id.toString()
            return true
          }
          return false
        })
        if (topicFirstLayerId) return true
        return false
      })
      console.log('topicFirstLayerId', topicFirstLayerId, topicId)
      result = getTopicHourRate({
        qualification,
        topicFirstLayerId,
        topicId,
        config,
        gradeGroup,
      })
      break
    }

    // todo, maybe keep these in default. Otherwise how to scale later?
    // case 'overseasStudy':
    // case 'teacherTraining':
    // case 'steam':
    // case 'academicPlanning':
    // case 'personalStatement':
    // case 'interest':
    default: {
      result = getCommonHourRate({
        qualification,
        config,
        topicId,
        gradeGroup,
      })
      break
    }
  }

  return result
}

export function calcDurationMin({start, end}: {start: string; end: string}) {
  return Math.floor((new Date(end).getTime() - new Date(start).getTime()) / 60000)
}

export const getCurriculumHourRate = ({qualification, curriculum, config, subject, gradeGroup}: any) => {
  if (!qualification) return 0
  const currentConfig = config?.hourRate?.filter((e: any) => e.qualification === qualification && e.curriculum === curriculum) || []
  const target = currentConfig.find((e: any) => e.default === false && e?.value?.includes(subject))

  const defaultTarget = currentConfig.find((e: any) => e.default === true)
  if (target?.gradeGroup?.some((e: string) => gradeGroup?.includes(e))) {
    return target?.price
  }
  return defaultTarget?.price || 0
}

export const getTopicHourRate = ({qualification, topicId, config, topicFirstLayerId, gradeGroup}: any) => {
  if (!qualification || !topicFirstLayerId) return 0
  const currentConfig = config?.hourRate?.filter((e: any) => e.qualification === qualification && e.topic === topicFirstLayerId) || []
  console.log('currentConfig', JSON.stringify(currentConfig))
  const target = currentConfig.find((e: any) => e.default === false && e?.value?.includes(topicId))
  console.log('target', target)
  const defaultTarget = currentConfig.find((e: any) => e.default === true)
  if (target?.gradeGroup?.some((e: string) => gradeGroup?.includes(e))) {
    console.log('target?.gradeGroup?', target?.price)
    return target?.price
  }
  console.log('defaultTarget?.price', defaultTarget?.price)
  return defaultTarget?.price || 0
}

export const getCommonHourRate = ({qualification, config, topicId, gradeGroup}: any) => {
  if (!qualification) return 0
  const currentConfig = config?.hourRate?.filter((e: any) => e.qualification === qualification) || []
  const target = currentConfig.find((e: any) => e.default === false && e?.value?.includes(topicId))
  const defaultTarget = currentConfig.find((e: any) => e.default === true)
  if (target?.gradeGroup?.some((e: string) => gradeGroup?.includes(e))) {
    return target?.price
  }
  return defaultTarget?.price || 0
}

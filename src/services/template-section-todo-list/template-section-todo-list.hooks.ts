import {HookContext} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
// Don't remove this comment. It's needed to format import lines nicely.

const {authenticate} = authentication.hooks
const restrictToOwner = async (context: HookContext) => {
  const {user} = context.params
  if (user) {
    context.params.query = {
      ...context.params.query,
      uid: user._id,
    }
  }
  return context
}

export default {
  before: {
    all: [authenticate('jwt')],
    find: [restrictToOwner],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}

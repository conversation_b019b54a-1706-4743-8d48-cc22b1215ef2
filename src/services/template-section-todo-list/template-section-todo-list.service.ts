import {ServiceAddons} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import {templateSectionTodoList} from './template-section-todo-list.class'
import createModel from '../../models/template-section-todo-list.model'
import hooks from './template-section-todo-list.hooks'

declare module '../../declarations' {
  interface ServiceTypes {
    'template-section-todo-list': templateSectionTodoList & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    whitelist: ['$exists', '$regex', '$options'],
    paginate: app.get('paginate'),
    _id: '_id',
    multi: ['remove'],
  }
  app.use('/template-section-todo-list', new templateSectionTodoList(options, app))
  const service = app.service('template-section-todo-list')

  service.hooks(hooks)
}

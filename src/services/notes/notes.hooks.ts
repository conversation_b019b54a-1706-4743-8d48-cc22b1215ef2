import {exec} from 'child_process'
import {Hook, HookContext} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
import hook from '../../hook'
// Don't remove this comment. It's needed to format import lines nicely.

const {authenticate} = authentication.hooks

export default {
  before: {
    all: [authenticate('jwt')],
    find: [],
    get: [],
    create: [
      async (context: HookContext) => {
        const {user} = context.params
        if (user?._id) {
          context.data.collaborators = [user._id]
        }
        return context
      },
    ],
    update: [],
    patch: [
      async (context: HookContext) => {
        // const {user} = context.params
        // if (user?._id) {
        //   await context.service.Model.updateOne({_id: context.id}, {$addToSet: {collaborators: user._id}})
        // }
        return context
      },
    ],
    remove: [],
  },
  after: {
    all: [],
    find: [
      async (context: HookContext) => {
        const {connection} = context.params
        const result = context.result
        for (const o of result.data) {
          if (connection && o && o._id) {
            const notesId = o._id.toString()
            context.app.channel(`notes/${notesId}`).join(connection)
          }
        }

        return context
      },
    ],
    get: [
      async (context: HookContext) => {
        const {connection} = context.params
        const result = context.result
        const notesId = result._id.toString()
        if (connection) {
          context.app.channel(`notes/${notesId}`).join(connection)
        }
      },
    ],
    patch: [
      async (context: HookContext) => {
        const {connection} = context.params
        const result = context.result
        const notesId = result._id.toString()
        console.log('oop', notesId, connection)
        if (connection) {
          context.app.channel(`notes/${notesId}`).join(connection)
        }
      },
    ],
  },
  error: {
    all: [],
  },
}

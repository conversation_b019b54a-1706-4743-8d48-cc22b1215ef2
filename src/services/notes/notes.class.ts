import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Note} from '../../types/note'

function bumpVersion(version: string, level: 'patch' | 'minor' | 'major' = 'patch'): string {
  let [major, minor, patch] = version.split('.').map((n) => parseInt(n, 10) || 0)

  switch (level) {
    case 'major':
      major++
      minor = 0
      patch = 0
      break
    case 'minor':
      minor++
      patch = 0
      break
    default: // patch
      patch++
  }

  return `${major}.${minor}.${patch}`
}

export class Notes extends Service<Note> {
  app: Application
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  // async patch(id: any, data: any, params: any) {
  //   const existing = (await this.Model.findById(id)) as Note | null

  //   if (!existing) {
  //     throw new Error('Note not found')
  //   }

  //   existing.history.push({
  //     note: existing.note,
  //     version: existing.version,
  //     savedBy: params?.user?._id || 'system',
  //     updatedTime: new Date(),
  //   })

  //   const newVersion = bumpVersion(existing.version || '0.0.0', 'patch')
  //   existing.note = data.note ?? existing.note
  //   existing.version = newVersion

  //   await existing.save()
  //   return existing
  // }
}

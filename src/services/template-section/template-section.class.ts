import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Id, Params} from '@feathersjs/feathers'
import {BadRequest, NotFound} from '@feathersjs/errors'

export class TemplateSection extends Service {
  app: Application

  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async create(data: any, params?: any): Promise<any> {
    const sectionDoc = await super.create(data, params)
    if (sectionDoc.pid) {
      if (sectionDoc.pid === sectionDoc.essayId) {
        await this.app.service('essay-template').Model.findByIdAndUpdate(sectionDoc.essayId, {$push: {sections: sectionDoc._id}}, {new: true})
        const mainTask = await this.app.service('associate-task-template').Model.findOne({
          pid: sectionDoc.pid,
          type: 'main',
        })
        if (mainTask) {
          await this.app.service('associate-task-template').Model.findByIdAndUpdate(mainTask._id, {$push: {sections: sectionDoc._id}}, {new: true})
          await this.Model.findByIdAndUpdate(sectionDoc._id, {$push: {atid: mainTask._id}}, {new: true})
        }
      } else {
        await this.Model.findByIdAndUpdate(sectionDoc._id, {$addToSet: {atid: sectionDoc.pid}}, {new: true})
        await this.app.service('associate-task-template').Model.findByIdAndUpdate(sectionDoc.pid, {$push: {sections: sectionDoc._id}}, {new: true})
      }
    }
    return sectionDoc
  }
  async patchSectionRubrics(data: {atid: string; rubricsData: any; sectionId: string}) {
    const {atid, rubricsData, sectionId} = data
    if (!atid || !sectionId || !rubricsData) {
      throw new Error('atid, sectionId and rubricsData are required')
    }
    const updatedSection = await this.Model.findOneAndUpdate({_id: sectionId}, {$set: {[`rubrics.${atid}.data`]: rubricsData}}, {new: true})
    if (!updatedSection) {
      throw new Error('Section not found or update failed')
    }
    return updatedSection
  }

  async remove(id: string, params?: Params): Promise<any> {
    const {query, app} = params || {}
    const importedAtid = query?.importedAtid

    try {
      if (!id) {
        throw new BadRequest('A valid section ID must be provided')
      }
      if (importedAtid) {
        await this.app.service('associate-task-template').Model.updateOne({_id: importedAtid}, {$pull: {sections: id}})
        await this.Model.updateOne({_id: id}, {$pull: {atid: importedAtid}})
      } else {
        const existing = (await this.Model.findOne({_id: id, del: {$ne: true}}).lean()) as any
        if (!existing) {
          throw new NotFound('Section not found')
        }
        if (existing.pid) {
          if (existing.essayId === existing.pid) {
            await this.app.service('essay-template').Model.updateMany({sections: id}, {$pull: {sections: id}})
            const essayTemplates = (await this.app
              .service('essay-template')
              .Model.find({associatedTasks: {$exists: true, $ne: []}})
              .lean()) as any
            const associatedTaskIds: any[] = []
            for (const template of essayTemplates) {
              if (Array.isArray(template.associatedTasks) && template.associatedTasks.length) {
                associatedTaskIds.push(...template.associatedTasks)
                await this.app
                  .service('associate-task-template')
                  .Model.updateMany({_id: {$in: template.associatedTasks}, sections: id}, {$pull: {sections: id}})
              }
            }
            await this.Model.updateOne(
              {_id: id},
              {
                $set: {del: true},
                $pullAll: {atid: associatedTaskIds},
              }
            )
          } else {
            await this.app.service('associate-task-template').Model.updateMany({sections: id}, {$pull: {sections: id}})
            await this.Model.updateOne(
              {_id: id},
              {
                $set: {del: true},
                $pull: {atid: existing.pid},
              }
            )
          }
        }
      }
      return {success: true}
    } catch (error) {
      console.error('Error in remove (template-section):', error)
      throw error
    }
  }
}

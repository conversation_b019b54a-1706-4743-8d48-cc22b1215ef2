import {ServiceAddons} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import {TemplateSection} from './template-section.class'
import createModel from '../../models/template-section.model'
import hooks from './template-section.hooks'

declare module '../../declarations' {
  interface ServiceTypes {
    'template-section': TemplateSection & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    whitelist: ['$exists', '$regex', '$options'],
    paginate: app.get('paginate'),
    _id: '_id',
    multi: ['remove'],
  }
  app.use('/template-section', new TemplateSection(options, app))
  const service = app.service('template-section')

  service.hooks(hooks)
}

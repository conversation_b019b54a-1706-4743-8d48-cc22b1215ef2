import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'

export class EssayTemplate extends Service {
  app: Application

  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async create(data: any, params?: any): Promise<any> {
    const essayDoc = await super.create(data, params)
    const associateTaskTemplate = await this.app.service('associate-task-template').Model.create({
      pid: essayDoc._id,
      type: 'main',
      serviceType: essayDoc.serviceType || null,
      subject: essayDoc.subject || null,
    })
    await this.Model.findByIdAndUpdate(essayDoc._id, {$push: {associatedTasks: associateTaskTemplate._id.toString()}}, {new: true})
    return {
      ...(essayDoc.toObject?.() || essayDoc),
      associateTaskTemplate,
    }
  }
}

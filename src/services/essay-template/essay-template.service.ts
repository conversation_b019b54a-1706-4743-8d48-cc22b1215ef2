import {ServiceAddons} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import {EssayTemplate} from './essay-template.class'
import createModel from '../../models/essay-template.model'
import hooks from './essay-template.hooks'

declare module '../../declarations' {
  interface ServiceTypes {
    'essay-template': EssayTemplate & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    whitelist: ['$exists', '$regex', '$options'],
    paginate: app.get('paginate'),
    _id: '_id',
    multi: ['remove'],
  }
  app.use('/essay-template', new EssayTemplate(options, app))
  const service = app.service('essay-template')

  service.hooks(hooks)
}

import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
const {NotFound, GeneralError, BadRequest} = require('@feathersjs/errors')

export class SessionTakeawaySnapshot extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  // Inform parents to view takeaway
  async getInformParents({_id, hash, studentId}: any, params: Params): Promise<any> {
    let takeaway: any = await this.Model.findById(_id).select(['uid', 'session', 'hash'])
    // let session: any = await this.app.service('session').Model.findOne({_id: takeaway.session})

    if (takeaway.hash == hash) return Promise.reject(new GeneralError('Same hash'))

    // 首次发送
    if (!takeaway.hash && hash) {
      await this.app.service('session').Model.updateOne({_id: takeaway.session}, {$inc: {'count.report': 1}})
    }

    // check income generation
    this.shouldGenerateBookingIncome(takeaway.session)

    this.sendTakeawayNotification(takeaway)

    return await this.Model.updateOne({_id}, {$set: {hash: hash}})

    // if (!student) return Promise.reject(new GeneralError('Not find student'))
    // const url = `${SiteUrl}/v2/account/takeaway/view/${_id}?studentId=${studentId}`
    // return await this.app.service('notice-tpl').mailto('InformParentsToViewTakeaway', student.parent.email, {
    //   username: student.parent.name.join(' '),
    //   student_name: student.name.join(' '),
    //   session_name: session.name,
    //   pwd: hash.slice(-4),
    //   url,
    // })
  }

  async shouldGenerateBookingIncome(sessionId: string) {
    // find session, select type, booking
    const session: any = await this.app.service('session').Model.findOne({_id: sessionId}).select(['type', 'booking', 'ended'])

    if (!session || !session.booking) return

    const sessionEnded = new Date(session.ended).getTime()
    const before24Hrs = new Date(Date.now() - 24 * 60 * 60 * 1000).getTime()

    if (sessionEnded > before24Hrs) {
      return
    }

    // find service-booking
    const booking: any = await this.app.service('service-booking').Model.findOne({_id: session.booking}).select(['booker', 'servicer', 'type', 'packUser'])
    if (!booking) return
    const booker = booking.booker

    // check if expected income log exists
    const expIncome: any = await this.app.service('income-log').Model.findOne({
      uid: booking.servicer,
      businessId: booking._id.toString(),
      category: 'teaching_service',
      status: 0,
    })
    if (!expIncome) return

    let accident: any = null
    let count = 0

    if (['bookingStuTask', 'bookingStuPdTask'].includes(session.type)) {
      count = await this.app.service('service-rating').Model.count({session: sessionId, booker: booker})
    }
    if (count !== 0) {
      accident = await this.app.service('teaching-accident').Model.findOne({session: session._id, student: booker})
      if (accident && accident.status === 'pending') {
        // waiting for SYS action
        return
      }
    }

    await this.app.service('income-log').Model.updateOne(
      {
        _id: expIncome._id.toString(),
      },
      {
        $set: {
          'eventDetails.takeaway': null,
        },
      }
    )

    let value = expIncome.value
    let notes: string | undefined
    if (accident && accident.status === 'approved') {
      value = 0
      notes = 'Amount change due to Teaching accident'
    }

    // generate income with actual expected amount
    this.app.service('income-log').generateIncome({
      source: 'booking_confirmed',
      snapshot: booking,
      otherDetails: {
        query: {
          _id: expIncome._id.toString(),
        },
        payload: {
          $set: {
            value,
            notes,
            status: 1,
            'eventDetails.sessionStatus': 'ended',
          },
        },
      },
    })
  }

  /** Send notification to students whenever takeway is sent */
  async sendTakeawayNotification(takeaway: any) {
    let session = await this.app.service('session').get(takeaway.session, {
      query: {$select: ['name']},
    })
    const student: any = await this.app.service('users').uidToInfo(takeaway.uid)
    await this.app.service('notice-tpl').mailto('TakeawaySentStudent', student, {
      username: student.name.join(' '),
      session_name: session?.name,
      url: `${SiteUrl}/v2/account/takeaway/${takeaway._id}?studentId=${takeaway.uid}`,
    })
  }

  async sendTakeawayReminder(data: any) {
    const {servicer, sessionName, booker, takeawayId} = data
    const serviceProvider: any = await this.app.service('users').uidToInfo(servicer)
    await this.app.service('notice-tpl').mailto('TakeawayReminder', serviceProvider, {
      username: serviceProvider.name.join(' '),
      session_name: sessionName,
      url: `${SiteUrl}/v2/account/takeaway/${takeawayId}/view/${booker}`,
    })
  }
}

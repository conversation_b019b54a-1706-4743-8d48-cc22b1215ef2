export function calculateRefundBreakUp(
  item: {
    price: number
    isPoint?: boolean
    giftCard?: number
    cash?: number
    refund: Array<{amount: number; method: string}>
    refundCash?: number // remove
  },
  refundAmount: number
) {
  if (!refundAmount || refundAmount <= 0 || item.isPoint)
    return {
      cashRefund: 0,
      giftCardRefund: 0,
    }
  let giftCardRefunded = 0
  if (Array.isArray(item.refund) && item.refund.length > 0) {
    giftCardRefunded = item.refund.reduce((sum, refund) => {
      return sum + (refund.method === 'giftCard' ? refund.amount : 0)
    }, 0)
  }
  if (refundAmount > item.price) {
    throw new Error('Invalid refund amount')
  }
  const actualRefundAmount = Math.min(refundAmount, item.price)

  const remainingGiftCard = (item.giftCard || 0) - giftCardRefunded
  const giftCardRefund = Math.min(actualRefundAmount, remainingGiftCard)
  const cashRefund = actualRefundAmount - giftCardRefund

  return {
    cashRefund,
    giftCardRefund,
  }
}

export const isIncomePending = (orderType: string, orderStatus: number, price: number) => {
  if (![200, 500, 501, 502].includes(orderStatus) || !price || price <= 0) {
    return null
  }
  let incomeStatus: string | null = null
  if (orderStatus === 200 && ['unit', 'session_self_study', 'premium_cloud', 'prompt'].includes(orderType)) {
    incomeStatus = 'actual_pending'
  } else if (['session_public'].includes(orderType)) {
    incomeStatus = orderStatus === 200 ? 'expected_pending' : 'actual_pending' // not exactly true for order cancellation
  }
  return incomeStatus
}

import {Application} from '../../../declarations'
import {queueForCommit, TxnParams} from '../../../hooks/dbTransactions'
import {calculateRefundBreakUp, isIncomePending} from './utils'
import {BadRequest, GeneralError} from '@feathersjs/errors'

export class OrderCancellation {
  constructor(private app: Application) {}

  // 主题服务包 按代金券退款 无积分 | function to cancel vouchers
  async cancelTicketController({tickets, status = 500}: any, params: TxnParams): Promise<any> {
    const options = Acan.getTxnOptions(params)
    let ticketData: any = await this.app.service('service-pack-ticket').Model.find({_id: {$in: tickets}}, null, options)
    // 验证tickets数据
    let orderId = ''
    for (let i = 0; i < ticketData.length; i++) {
      const e = ticketData[i]
      if (e.refund) {
        return Promise.reject(new GeneralError('Already refunded'))
      }
      if (!e.order) {
        return Promise.reject(new GeneralError('Order not found'))
      }
      if (!orderId) {
        orderId = e.order
      } else {
        if (orderId != e.order) {
          return Promise.reject(new GeneralError('Order not match'))
        }
      }
      if (e.uid) {
        return Promise.reject(new GeneralError('Please unclaim first to cancel'))
      }
    }
    let order: any = await this.app.service('order').Model.findOne({_id: orderId}, null, options).lean()
    if (new Date(order.createdAt).getTime() + 14 * 86400 * 1000 < Date.now()) {
      return Promise.reject(new GeneralError('Cancellation is not allowed after 14 days.'))
    }
    let linkDict: any = {}
    let refundPriceByService: any = {}
    order.links.forEach((e: any) => {
      linkDict[e.id] = e
    })

    // 计算退款金额
    let refundPrice = 0
    for (let i = 0; i < ticketData.length; i++) {
      // const ticket = ticketData[i]
      let {serviceData} = ticketData[i]
      for (let j = 0; j < serviceData.length; j++) {
        let {servicePack, cash, cashOrigin, expired} = serviceData[j]
        let link = linkDict[servicePack]
        let totalCount = cashOrigin || link.count
        let remainingCount = cash
        let usedCount = totalCount - remainingCount
        let {goods, discountConfig, price, persons} = link
        let itemPrice = price / persons
        discountConfig = discountConfig ?? goods.discountConfig
        let discount = goods.discount
        let itemRefundPrice = 0
        if (expired) continue
        // Check if batch discounts exist in link.discount array
        if (discount && Array.isArray(discount) && discount.length > 0 && discount.some((e: any) => e.discount > 0)) {
          // Apply batch discount logic (same as calcPriceServiceRefund)
          let {price: baseUnitPrice} = goods
          let effectiveUnitPriceForUsedItems = baseUnitPrice

          let current: any = false
          let discountSort = discount.sort((a: any, b: any) => a.count - b.count)
          /**
           * todo
           * add one for count: 1 in db and check
           */
          // Find the applicable batch discount based on USED count
          for (let k = 0; k < discountSort.length; k++) {
            const item = discountSort[k]
            if (usedCount >= item.count) {
              current = item
            } else if (usedCount < item.count) {
              break
            }
          }

          // Apply batch discount to get effective unit price for used items
          if (current) {
            effectiveUnitPriceForUsedItems = (baseUnitPrice * (100 - current.discount)) / 100
          }

          // Apply discountConfig if exists
          let isDiscountConfig =
            discountConfig && discountConfig.enable && (!discountConfig.end || new Date(order.createdAt).getTime() < new Date(discountConfig.end).getTime())
          // Calculate refund: total paid - (effective price for used items × used count × persons)
          itemRefundPrice = totalCount
            ? Number((itemPrice - effectiveUnitPriceForUsedItems * usedCount * (isDiscountConfig ? (100 - discountConfig.discount) / 100 : 1)).toFixed(0))
            : 0
        } else {
          // Fallback to original proportional logic if no batch discounts
          itemRefundPrice = Number(((cash / totalCount) * itemPrice).toFixed(0))
        }
        refundPrice += itemRefundPrice
        refundPriceByService[servicePack] = refundPriceByService[servicePack] ? refundPriceByService[servicePack] + itemRefundPrice : itemRefundPrice
      }
    }
    /**
     * if 'uid' exists, should not be able to cancel, throw error above
     * we have not removed the old logic below, in case we need it in future
     */
    // 已绑定用户ticket,user-data更新
    for (let i = 0; i < ticketData.length; i++) {
      let {uid, serviceData} = ticketData[i]
      for (let j = 0; j < serviceData.length; j++) {
        let {servicePack, cash, point, gift} = serviceData[j]
        if (uid) {
          let packUserData: any = await this.app.service('service-pack-user').Model.findOne({uid, 'snapshot._id': servicePack}, null, options)
          let unused = Number((cash + point + gift).toFixed(0))
          await this.app.service('service-pack-user-data').used(
            {
              packUser: packUserData._id,
              type: 'refund',
              times: unused,
              order: orderId,
            },
            params
          )
        }
        if (!uid) {
          await this.app.service('service-pack').Model.updateOne({_id: servicePack}, {$inc: {'count.ticket': -1}}, options)
        }
      }
    }

    // 更新link.refundPrice
    for (let i = 0; i < order.links.length; i++) {
      let link = order.links[i]
      if (refundPriceByService[link.id]) {
        link.refundPrice = (link.refundPrice + refundPriceByService[link.id]).toFixed(0)
        if (link.refundPrice > link.price) {
          throw new GeneralError('invalid refund amount')
        }
      }
    }
    const patchData: any = {
      links: order.links,
    }
    const {cashRefund, giftCardRefund} = calculateRefundBreakUp(
      {
        price: order.price,
        giftCard: order.priceBreakdown?.giftCard,
        cash: order.priceBreakdown?.cash,
        refund: order.refund,
      },
      refundPrice
    )
    if (cashRefund > 0) {
      patchData.refundRequired = {
        amount: cashRefund,
        invalidLinks: [],
        giftCardRefunded: giftCardRefund,
      }
      await queueForCommit(this.app, 'order', 'processRefund', [orderId, {amount: cashRefund, giftCardRefunded: giftCardRefund}, order], params)
    }

    if (giftCardRefund > 0) {
      await this.app.service('gift-card-log').createGiftCardLog(
        {
          uid: order.buyer,
          tab: 'earn',
          source: 'order',
          category: 'order_canceled_refund',
          value: giftCardRefund,
          businessId: order._id.toString(),
          isSchool: order.isSchool,
          // snapshot: order,
        },
        params
      )
      patchData.$push = {refund: {method: 'giftCard', amount: giftCardRefund, executed: true, status: status, createdAt: new Date(), executedAt: new Date()}}
    }
    await Promise.all([
      this.app.service('order').Model.updateOne({_id: orderId}, patchData, options),
      this.app.service('service-pack-ticket').Model.updateMany({_id: {$in: tickets}}, {refund: true, $unset: {uid: ''}}, options),
      this.app.service('service-pack-apply').Model.updateMany({serviceTicket: {$in: tickets}}, {$pull: {serviceTicket: {$in: tickets}}}, options),
    ])

    // statistics update
    if (!order.isPoint && order.servicePremium) {
      let premiumType = 'all'
      if (order.isSchool) {
        premiumType = this.app.service('order').getServicePremiumType(order.links)
      }
      queueForCommit(
        this.app,
        'service-pack',
        'Model.updateOne',
        [
          {_id: order.servicePremium, statistic: {$elemMatch: {count: 1, isSchool: order.isSchool, type: premiumType}}},
          {$inc: {income: refundPrice * -1, 'statistic.$.income': refundPrice * -1}},
        ],
        params
      )
    }
    if (!order.isPoint) {
      Object.keys(refundPriceByService).forEach(async (linkId) => {
        if (!refundPriceByService[linkId]) return
        const link = linkDict[linkId]
        queueForCommit(
          this.app,
          'service-pack',
          'Model.updateOne',
          [
            {_id: link.id, statistic: {$elemMatch: {count: link.count, isSchool: order.isSchool, type: 'all', city: link.city}}},
            {$inc: {income: refundPriceByService[linkId] * -1, 'statistic.$.income': refundPriceByService[linkId] * -1}},
          ],
          params
        )
      })
    }

    let updatedOrderStatus = order.status
    // ticket全部退完以后 把订单剩余部分退款
    let notRefundTicket = await this.app.service('service-pack-ticket').Model.find({order: orderId, refund: false}, null, options)
    if (notRefundTicket.length == 0) {
      let isAllRefund = true
      let links = order.links.map((e: any) => {
        if (e.style == 'service') {
          e.removed = true
        }
        if (!e.removed) {
          isAllRefund = false
        }
        return e
      })
      const patchData: any = {
        links,
      }
      if (isAllRefund) {
        patchData.status = status
        updatedOrderStatus = status
      }
      await this.app.service('order').Model.updateOne({_id: orderId}, patchData, options)
    }
    return {ok: 1, status: updatedOrderStatus}
  }

  // function to cancel order
  async cancelOrderController({id, status, linkIds, childSessions, updateIncome = true}: any, params: TxnParams): Promise<any> {
    console.log('cancelOrderController', id, status, linkIds, JSON.stringify(childSessions))
    // 检查订单是否可以退款，获取退款相关信息
    let {refundAllowed, message, order, refundPrice, refundPoint, refundLinkName, refundLinkCover} = await this.app.service('order').getOrderRefundCheck(
      {
        id,
        status,
        linkIds,
        childSessions,
      },
      {...Acan.mergeTxnParams(params), provider: null}
    )
    // 不允许退款
    if (!refundAllowed) {
      return Promise.reject(new GeneralError(message))
    }
    // 获取事务参数
    const options = Acan.getTxnOptions(params)

    // 退款列表
    let refundList = []
    // 判断是否所有商品都被移除
    let isAllRefund = order.links.every((e: any) => e.removed)
    // 如果订单未支付
    if (order.paid === 0) {
      // 准备更新数据
      let patchData: any = {links: order.links}
      // 如果全部退款，更新订单状态
      if (isAllRefund) {
        patchData.status = status
      }
      // 更新订单
      await this.app.service('order').Model.updateOne({_id: id}, patchData, options)
      // 处理退款相关业务逻辑
      await this.handleLinkRefund(order, refundPrice, params)
      return {success: true, id, status: patchData.status ?? order.status}
    } else if (order.paid == 1) {
      // 如果订单已支付
      if (order.isPoint) {
        // 积分退款处理
        // 添加积分日志
        await this.app.service('point-log').getAddLog({
          tab: 'earn',
          uid: order.buyer,
          source: 'refund',
          category: 'refund',
          change: refundPoint,
          businessId: order._id,
          snapshot: order,
        })
        // 添加到退款列表
        refundList.push({
          method: 'point',
          amount: refundPoint,
          createdAt: new Date(),
          executedAt: new Date(),
          executed: true,
          status: status,
        })
      }
      // 准备更新数据
      let patchData: any = {links: order.links}
      // 如果全部退款，更新订单状态和支付状态
      if (isAllRefund) {
        patchData.status = status
        patchData.paid = 2
      }

      const {cashRefund, giftCardRefund} = calculateRefundBreakUp(
        {
          price: order.price,
          isPoint: order.isPoint,
          giftCard: order.priceBreakdown?.giftCard,
          cash: order.priceBreakdown?.cash,
          refund: order.refund,
        },
        refundPrice
      )
      // cash refund
      if (cashRefund > 0) {
        patchData.refundRequired = {
          amount: refundPrice,
          refundLinkName,
          refundLinkCover,
          invalidLinks: [],
          giftCardRefunded: giftCardRefund,
        }
        queueForCommit(
          this.app,
          'order',
          'processRefund',
          [id, {amount: cashRefund, refundLinkName, refundLinkCover, giftCardRefunded: giftCardRefund}, order],
          params
        )
      }
      // gift card refund
      if (giftCardRefund > 0) {
        await this.app.service('gift-card-log').createGiftCardLog(
          {
            uid: order.buyer,
            tab: 'earn',
            source: 'order',
            category: 'order_canceled_refund',
            value: giftCardRefund,
            businessId: order._id.toString(),
            isSchool: order.isSchool,
            // snapshot: order,
          },
          params
        )
        refundList.push({
          method: 'giftCard',
          amount: giftCardRefund,
          createdAt: new Date(),
          executedAt: new Date(),
          executed: true,
          status: status,
        })
      }

      if (giftCardRefund > 0 && cashRefund <= 0) {
        queueForCommit(
          this.app,
          'order',
          'sendRefundSuccessNotification',
          [{order, amount: 0, refundLinkName, refundLinkCover, giftCardRefunded: giftCardRefund}, 'api'],
          params
        )
      }

      if (refundList.length > 0) {
        patchData.$push = {refund: {$each: refundList}}
      }

      // 更新订单
      await this.app.service('order').Model.updateOne({_id: id}, patchData, options)

      // income job creation
      console.log('isIncomePending', order.type, patchData.status, order.price)
      if (updateIncome && isIncomePending(order.type, status, order.price)) {
        console.log('creating income job')
        queueForCommit(
          this.app,
          'income-log',
          'generateIncome',
          [
            {
              source: 'order',
              snapshot: {...order, links: Acan.clone(patchData.links), status: status},
              otherDetails: {
                childSessions,
              },
              logStatus: 1,
            },
          ],
          params
        )
      }
      // 处理退款相关业务
      await this.handleLinkRefund(order, refundPrice, params)

      // 退款成功 返回结果
      return {success: true, id, status: patchData.status ?? order.status}
    }
  }

  // 退款后处理公开课和service相关业务解绑 | updates to cancel/undeliver the order
  async handleLinkRefund(order: any, refundPrice: any, params: TxnParams) {
    const options = Acan.getTxnOptions(params)
    for (let i = 0; i < order.links.length; i++) {
      let link = order.links[i]
      if (!link.pending) continue

      if (link.style === 'unit') {
        if (!order.isPoint && link.price) {
          await this.app.service('unit').Model.updateOne({_id: link.id}, {$inc: {income: link.price * -1}}, options)
        }
      } else if (link.style === 'session') {
        if (link.removed) await this.app.service('session').getUnReg({_id: link.id}, params)
        if (!order.isPoint && link.price) {
          await this.app.service('session').Model.updateOne({_id: link.id}, {$inc: {income: (link.currentRefundAmount ?? link.price) * -1}}, options)
        }
      } else if (['section_top_up', 'remaining_sections'].includes(link.style) || (link.style === 'service' && link.type === 'serviceTask')) {
        if (order.isSchool) {
          continue
        }
        await this.handleServiceTaskRefund({orderId: order._id}, params)
        let premiumType = 'all'
        await this.app
          .service('service-pack')
          .Model.updateOne(
            {_id: link.id, statistic: {$elemMatch: {count: link.count, isSchool: order.isSchool, type: premiumType, city: link.city}}},
            {$inc: {income: link.refundPrice * -1, 'statistic.$.income': link.refundPrice * -1}},
            options
          )
      } else if (link.style === 'service' || link.style === 'service_substitute') {
        let packUser: any = await this.app
          .service('service-pack-user')
          .Model.findOne({uid: order.buyer, 'snapshot._id': link.id, pid: {$exists: false}}, null, options)
        if (link.isOnCampus) {
          packUser = await this.app.service('service-pack-user').Model.findOne({pid: packUser._id, country: link.country, city: link.city}, null, options)
        }
        if (link.style === 'service') {
          if (order.isSchool) {
            continue
          }
          // 无主题
          let serviceCount = await this.app.service('order').calcServiceCount({packUser, orderId: order._id}, params)
          // let times: any = (link.count + link.giftCount - packUser.used).toFixed(0)
          // this.app.service('service-pack-user').used({_id: packUser._id, times: times * 1, type: 'refund'})
          if (serviceCount.unused > 0) {
            await this.app.service('service-pack-user-data').used(
              {
                packUser: packUser._id,
                type: 'refund',
                times: serviceCount.unused,
                order: order._id,
              },
              params
            )
          }
        } else {
          // 代课
          let serviceCount = await this.app.service('order').calcServiceCountSubstitute(packUser, link, order.isPoint)
          await this.app.service('service-pack-user-data').usedSubstitute({packUser: packUser._id, times: serviceCount.unused, type: 'refund'}, params)
        }
        // if (link.promotion) {
        //   let userData: any = await this.app.service('users').Model.findOne({_id: order.buyer})
        //   let freeServiceType = userData.freeServiceType
        //   delete freeServiceType[link.id]
        //   await this.app.service('users').Model.updateOne({_id: order.buyer}, {freeServiceType})
        // }
        if (!order.isPoint) {
          let premiumType = 'all'
          await this.app
            .service('service-pack')
            .Model.updateOne(
              {_id: link.id, statistic: {$elemMatch: {count: link.count, isSchool: order.isSchool, type: premiumType, city: link.city}}},
              {$inc: {income: link.refundPrice * -1, 'statistic.$.income': link.refundPrice * -1}},
              options
            )
        }
      } else if (link.style === 'service_premium') {
        let packUser: any = await this.app.service('service-pack-user').Model.findOne({uid: order.buyer, order: order._id, premium: link.id}, null, options)
        let serviceCount = await this.app.service('order').calcServiceCount({packUser, orderId: order._id}, params)
        if (serviceCount.unused > 0) {
          await this.app.service('service-pack-user-data').used(
            {
              packUser: packUser._id,
              type: 'refund',
              times: serviceCount.unused,
              order: order._id,
            },
            params
          )
        }
      }
      delete link.pending
      delete link.currentRefundAmount
    }

    // 解绑主题服务包面试数据
    if (order.servicePackApply) {
      await this.app
        .service('service-pack-apply')
        .Model.updateOne({_id: order.servicePackApply}, {$unset: {interviewOrder: ''}}, options)
        .exec()
    }
    if (!order.isPoint && order.servicePremium) {
      let premiumType = 'all'
      if (order.isSchool) {
        premiumType = this.app.service('order').getServicePremiumType(order.links)
      }
      await this.app
        .service('service-pack')
        .Model.updateOne(
          {_id: order.servicePremium, statistic: {$elemMatch: {count: 1, isSchool: order.isSchool, type: premiumType}}},
          {$inc: {income: refundPrice * -1, 'statistic.$.income': refundPrice * -1}},
          options
        )
    }
  }

  // handle service task refund by order id
  async handleServiceTaskRefund({orderId}: {orderId: string}, params: TxnParams) {
    const options = Acan.getTxnOptions(params)
    const sections = await this.app
      .service('section')
      .Model.find({'creditSources.orderId': orderId, status: ['pending', 'ongoing']}, null, options)
      .lean()

    // Check if all sections are pending - throw error if any are ongoing
    const isOngoing = sections.some((section: any) => section.status === 'ongoing')
    if (isOngoing) {
      throw new BadRequest(`Cannot refund order ${orderId} as the task is ongoing`)
    }

    const sectionUpdates = []
    const serviceTaskIds = new Set()

    // Process each section for refund
    for (const section of sections) {
      const sectionData = section as any
      // Find the credit source for this order
      const creditSourceIndex = sectionData.creditSources.findIndex((cs: any) => cs.orderId.toString() === orderId.toString() && cs.isActive)

      if (creditSourceIndex === -1) continue

      const creditSource = sectionData.creditSources[creditSourceIndex]
      const refundAmount = creditSource.available

      serviceTaskIds.add(sectionData.serviceTaskId.toString())

      // Calculate new available credits after refund
      const newAvailableCredits = sectionData.availableCredits - refundAmount
      const newStatus = newAvailableCredits === 0 ? 'refunded' : sectionData.status

      // Prepare section update
      const updateOperation = {
        $set: {
          [`creditSources.${creditSourceIndex}.isActive`]: false,
          [`creditSources.${creditSourceIndex}.available`]: 0,
          availableCredits: newAvailableCredits,
          status: newStatus,
        },
      }

      sectionUpdates.push({
        sectionId: sectionData._id,
        updateOperation,
        newAvailableCredits,
        salesPrice: sectionData.salesPrice,
        serviceTaskId: sectionData.serviceTaskId,
      })
    }

    // Execute all section updates
    await Promise.all(
      sectionUpdates.map(({sectionId, updateOperation}) => this.app.service('section').Model.updateOne({_id: sectionId}, updateOperation, options))
    )

    // Process service-pack-user updates
    for (const serviceTaskId of serviceTaskIds) {
      // Check remaining pending sections for this service task
      const remainingSections = await this.app.service('section').Model.find({serviceTaskId, status: 'pending'}, null, options).lean()

      const servicePackUserUpdate: any = {}

      // If no pending sections remain, mark as refunded
      if (remainingSections.length === 0) {
        servicePackUserUpdate.associatedTaskStatus = 'refunded'
      }

      // Check for deficit sections and update sectionCreditDeficit
      const deficitSection = sectionUpdates.find(
        (update) => update.serviceTaskId.toString() === serviceTaskId && update.salesPrice > update.newAvailableCredits && update.newAvailableCredits > 0
      )

      if (deficitSection) {
        servicePackUserUpdate.sectionCreditDeficit = {
          sectionId: deficitSection.sectionId,
          points: deficitSection.salesPrice - deficitSection.newAvailableCredits,
        }
      }

      // Update service-pack-user if there are changes
      if (Object.keys(servicePackUserUpdate).length > 0) {
        await this.app.service('service-pack-user').Model.updateOne({_id: serviceTaskId}, {$set: servicePackUserUpdate}, options)
      }
    }
  }

  // calculate service task refund price and validity
  async calcServiceTaskRefund({order, discountConfig}: {order: any; discountConfig: any}, params?: TxnParams) {
    const options = Acan.getTxnOptions(params)
    const sections: any[] = await this.app
      .service('section')
      .Model.find({'creditSources.orderId': order._id, status: ['pending', 'ongoing']}, null, options)
      .lean()
    if (sections.length === 0) {
      return {refundPrice: 0, valid: false, message: 'No section available for cancellation'}
    }

    let refundPrice = 0
    for (let i = 0; i < sections.length; i++) {
      const section = sections[i]
      if (section.status === 'ongoing') {
        return {refundPrice: 0, valid: false, message: "Cannot cancel - Associated task's section has active bookings"}
      }
      refundPrice += section.creditSources.reduce((total: number, item: any) => {
        if (item.orderId.toString() === order._id.toString()) {
          return total + item.available
        }
        return total
      }, 0)
    }
    // apply discount if available at the time of purchase
    if (discountConfig && discountConfig.enable && (!discountConfig.end || new Date(order.createdAt).getTime() < new Date(discountConfig.end).getTime())) {
      refundPrice = (refundPrice * (100 - discountConfig.discount)) / 100
    }
    // if over 30 days, refund 70%
    if (new Date(order.createdAt).getTime() + 30 * 24 * 60 * 60 * 1000 < Date.now()) {
      refundPrice = (refundPrice * 70) / 100
    }
    return {refundPrice: refundPrice * 100, valid: true}
  }

  getRemaingRefundAmount({isPoint, link}: {isPoint: boolean; link: any}) {
    if (isPoint) {
      return link.point - (link.refundPoint || 0)
    } else {
      return link.price - (link.refundPrice || 0)
    }
  }

  // calculate session refund price and validity
  async calcSessionRefund(
    {order, link, status, childSessions}: {order: any; link: any; status: number; childSessions?: any[]},
    params?: TxnParams
  ): Promise<any> {
    const options = Acan.getTxnOptions(params)
    // 可退款判定 并同意退款
    if (status === 500) {
      let session: any = await this.app.service('session').Model.findById(link.id, null, options).select(['start']) //get(link.id, Acan.mergeTxnParams(params))
      if (session?.start && new Date(session.start).getTime() - 12 * 60 * 60 * 1000 < Date.now()) {
        return {
          valid: false,
          response: {
            refundAllowed: false,
            cancelDisabled: true,
            separateAllowed: new Date(new Date(session.start).getTime()) <= new Date(),
            message: 'Free cancellation within 12 hours before the start of the workshop.',
          },
        }
      }

      return {
        valid: true,
        refundAmount: this.getRemaingRefundAmount({isPoint: order.isPoint, link}),
      }
    }

    if (!childSessions?.length) {
      return {
        valid: true,
        refundAmount: this.getRemaingRefundAmount({isPoint: order.isPoint, link}),
      }
    }

    const childIds = childSessions.map((v: any) => v._id)
    console.log('childIds', childIds, link.refundedItems)
    if (link.refundedItems?.some((item: any) => childIds.includes(item._id))) {
      console.log('refundedItems')
      return {
        valid: false,
      }
    }

    const liveSessions = link.goods?.childs?.length ? link.goods.childs.filter((v: any) => v.sessionType === 'live') : []
    console.log('liveSessions', liveSessions.length)
    const allLiveSessionIds = liveSessions.map((v: any) => v._id)
    console.log('allLiveSessionIds', allLiveSessionIds)
    if (!allLiveSessionIds.length || childIds.some((v: any) => !allLiveSessionIds.includes(v))) {
      console.log('liveSessions.some')
      return {
        valid: false,
      }
    }

    const refundAmount = Math.floor(((order.isPoint ? link.point : link.price) / liveSessions.length) * childSessions.length)
    console.log('refundAmount', refundAmount, liveSessions.length, childSessions.length)
    const totalRefundedItems = link.refundedItems?.length ? link.refundedItems.concat(childSessions) : childSessions
    console.log('totalRefundedItems', totalRefundedItems.length, link.refundedItems?.length)
    return {
      valid: true,
      refundAmount,
      isRemaining: totalRefundedItems.length < liveSessions.length,
      refundedItems: totalRefundedItems,
    }
  }
}

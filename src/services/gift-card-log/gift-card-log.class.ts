import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {BadRequest} from '@feathersjs/errors'
import {TxnParams} from '../../hooks/dbTransactions'
import {ObjectID} from 'bson'

interface CreateGiftCardLog {
  uid: string
  tab: string
  source: string
  category: string
  value: number
  businessId?: string
  isSchool: boolean
  snapshot?: any
  reserveBalance?: boolean
}

export class GiftCardLog extends Service {
  app: Application

  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async createGiftCardLog(data: CreateGiftCardLog, params?: TxnParams): Promise<any> {
    const {uid, tab, source, category, value, businessId, snapshot, isSchool, reserveBalance} = data

    if (!uid || !tab || !source || !category || value === undefined) {
      throw new BadRequest('Missing required fields')
    }

    const numericValue = Number(value)
    const options = Acan.getTxnOptions(params)

    // Generate a unique transaction ID for this operation
    const transactionId = new ObjectID().toString()

    // Update wallet balance
    let updatedBalance: any
    if (reserveBalance && numericValue < 0) {
      // Reserve the balance (move from available to reserved)
      updatedBalance = await this.app.service('wallet-balance').reserveBalance(uid, isSchool, 'giftCard', Math.abs(numericValue), transactionId, params)
    } else if (tab === 'earn' || tab === 'claim') {
      // Add to available balance
      updatedBalance = await this.app
        .service('wallet-balance')
        .updateBalance({uid, isSchool, balanceType: 'giftCard', amount: numericValue, transactionId, tab: tab}, params)
    } else {
      throw new BadRequest('Invalid tab value')
    }

    // Create the log entry with the same transaction ID
    const logEntry = await this.Model.create(
      [
        {
          _id: new ObjectID(transactionId), // Use the same ID as transaction
          uid,
          tab,
          source,
          category,
          value: numericValue,
          total: updatedBalance.availableBalance, // totalBalance
          businessId,
          snapshot,
          isSchool: isSchool || false,
        },
      ],
      options
    )

    return {
      logEntry: logEntry[0],
      updatedBalance,
      transactionId,
    }
  }

  // Release reserved balance and create log entry
  async releaseReservedBalance(uid: string, isSchool: boolean, amount: number, businessId?: string, params?: TxnParams): Promise<any> {
    const numericAmount = Number(amount)
    const options = Acan.getTxnOptions(params)

    if (numericAmount <= 0) {
      throw new BadRequest('Release amount must be positive')
    }

    // Generate a unique transaction ID for this operation
    const transactionId = new ObjectID().toString()

    // Release the reserved balance
    const updatedBalance = await this.app.service('wallet-balance').releaseReservedBalance(uid, isSchool, 'giftCard', numericAmount, transactionId, params)

    // Create the log entry for the release
    const logEntry = await this.Model.create(
      [
        {
          _id: new ObjectID(transactionId), // Use the same ID as transaction
          uid,
          tab: 'earn', // Released balance goes back to available (earn)
          source: 'order',
          category: 'reserved_balance_release',
          value: numericAmount,
          total: updatedBalance.availableBalance,
          businessId,
          isSchool,
        },
      ],
      options
    )

    return {
      logEntry: logEntry[0],
      updatedBalance,
      transactionId,
    }
  }

  // Deduct reserved balance WITHOUT creating a log (used when payment is confirmed)
  async deductReservedBalance(uid: string, isSchool: boolean, amount: number, params?: TxnParams): Promise<any> {
    const numericAmount = Number(amount)

    if (numericAmount <= 0) {
      throw new BadRequest('Deduct amount must be positive')
    }

    // Just deduct from wallet-balance, no log entry needed
    const updatedBalance = await this.app.service('wallet-balance').deductReservedBalance(uid, isSchool, 'giftCard', numericAmount, params)

    return {
      updatedBalance,
    }
  }
}

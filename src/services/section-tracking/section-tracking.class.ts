import {HookContext} from '@feathersjs/feathers'
import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {commitTransactionSession, rollbackTransactionSession, startTransactionSession, TxnParams} from '../../hooks/dbTransactions'

export class SectionTracking extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }
}

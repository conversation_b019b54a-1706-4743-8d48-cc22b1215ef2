import {HookContext} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
import hook from '../../hook'
// Don't remove this comment. It's needed to format import lines nicely.

const {authenticate} = authentication.hooks

export default {
  before: {
    all: [authenticate('jwt')],
    find: [],
    get: [hook.toClass],
    create: [
      hook.disallowExternal,
      async (d: HookContext) => {
        if (!d.data) {
          return d
        }

        let docs: any
        if (Array.isArray(d.data)) {
          d.data.forEach((doc: any) => {
            if (doc.creditedPoints > 0) {
              if (!docs) docs = []
              docs.push(doc)
            }
          })
        } else if (d.data?.creditedPoints > 0) {
          docs = d.data
        }
        d.app.service('income-log').generateIncome({
          source: 'section-tracking',
          snapshot: docs,
        })
        return d
      },
    ],
    update: [hook.disable],
    patch: [hook.disallowExternal],
    remove: [hook.disallowExternal],
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}

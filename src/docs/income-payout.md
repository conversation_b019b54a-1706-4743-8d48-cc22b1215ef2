### Payout Stategy

```js
// Monthly calculation cron (runs on 1st or 2nd of every month)
const calculateMonthlyPayouts = async () => {
  const lastMonth = new Date()
  lastMonth.setUTCMonth(lastMonth.getUTCMonth() - 1)
  lastMonth.setUTCDate(28)
  lastMonth.setUTCHours(23, 59, 59, 999)

  const snapshotDate = new Date(lastMonth)

  // Get all users who might have earnings
  const usersWithEarnings = await IncomeLog.distinct('uid', {
    status: 1,
    tab: 'earn',
    actualAt: {$lte: snapshotDate},
  })

  for (const uid of usersWithEarnings) {
    await calculateUserPayout(uid, snapshotDate)
  }
}

const calculateUserPayout = async (uid, snapshotDate) => {
  const session = await mongoose.startSession()

  try {
    await session.withTransaction(async () => {
      // Find last completed payout
      const lastPayout = await PayoutLog.findOne({
        uid,
        status: 'completed',
      })
        .sort({snapshotDate: -1})
        .session(session)

      const fromDate = lastPayout?.snapshotDate || new Date(0)

      // Calculate earnings up to snapshot date
      const result = await IncomeLog.aggregate([
        {
          $match: {
            uid,
            status: 1,
            tab: 'earn',
            actualAt: {
              $gt: fromDate,
              $lte: snapshotDate,
            },
          },
        },
        {$group: {_id: null, amount: {$sum: '$value'}}},
      ]).session(session)

      const unpaidAmount = result[0]?.amount || 0

      if (unpaidAmount >= 500) {
        // Minimum $5.00
        const scheduledDate = new Date(snapshotDate)
        scheduledDate.setUTCDate(8) // 8th of current month
        scheduledDate.setUTCMonth(scheduledDate.getUTCMonth() + 1)

        await PayoutLog.create(
          [
            {
              uid,
              airwallexBeneficiaryId: await getUserAirwallexId(uid),
              amount: unpaidAmount,
              snapshotDate,
              scheduledPayoutDate: scheduledDate,
              status: 'pending',
            },
          ],
          {session}
        )
      }
    })
  } catch (error) {
    console.error(`Failed to calculate payout for user ${uid}:`, error)
  } finally {
    await session.endSession()
  }
}

// Actual payout processing cron (runs on 8th of every month)
const processScheduledPayouts = async () => {
  const today = new Date()
  today.setUTCHours(0, 0, 0, 0)

  const tomorrow = new Date(today)
  tomorrow.setUTCDate(tomorrow.getUTCDate() + 1)

  const pendingPayouts = await PayoutLog.find({
    status: 'pending',
    scheduledPayoutDate: {
      $gte: today,
      $lt: tomorrow,
    },
  })

  for (const payout of pendingPayouts) {
    await processSinglePayout(payout)
  }
}

const processSinglePayout = async (payout) => {
  const session = await mongoose.startSession()

  try {
    await session.withTransaction(async () => {
      // Update payout status to processing
      await PayoutLog.findByIdAndUpdate(
        payout._id,
        {
          status: 'processing',
          sentAt: new Date(),
        },
        {session}
      )

      // Call Airwallex API
      const airwallexResponse = await callAirwallexAPI({
        beneficiaryId: payout.airwallexBeneficiaryId,
        amount: payout.amount,
      })

      if (airwallexResponse.success) {
        // Update payout to completed
        await PayoutLog.findByIdAndUpdate(payout._id, {status: 'completed'}, {session})

        // Add claim entry to income-log for UI
        await IncomeLog.create(
          [
            {
              uid: payout.uid,
              tab: 'claim',
              category: 'monthly_payout',
              value: -payout.amount,
              total: await calculateNewTotal(payout.uid, -payout.amount, session),
              actualAt: new Date(),
              status: 1,
              businessId: payout._id.toString(),
            },
          ],
          {session}
        )
      } else {
        await PayoutLog.findByIdAndUpdate(payout._id, {status: 'failed'}, {session})
      }
    })
  } catch (error) {
    console.error(`Failed to process payout ${payout._id}:`, error)
    await PayoutLog.findByIdAndUpdate(payout._id, {status: 'failed'})
  } finally {
    await session.endSession()
  }
}

const calculateNewTotal = async (uid, value, session) => {
  const lastEntry = await IncomeLog.findOne({uid}).sort({actualAt: -1, _id: -1}).session(session)

  return (lastEntry?.total || 0) + value
}

// Utility function to get user's Airwallex beneficiary ID
const getUserAirwallexId = async (uid) => {
  // Implementation depends on your user model
  const user = await User.findById(uid).select('airwallexBeneficiaryId')
  return user?.airwallexBeneficiaryId
}

// Mock Airwallex API call
const callAirwallexAPI = async (payoutData) => {
  // Your Airwallex integration here
  return {success: true, transactionId: 'mock-tx-id'}
}
```

### Balance Aggregation

```js
// Alternative: Include all transactions (earn + claim) for complete balance
const getUnpaidBalanceComplete = async (uid) => {
  const lastCompletedPayout = await PayoutLog.findOne({
    uid,
    status: 'completed',
  }).sort({snapshotDate: -1})

  const fromDate = lastCompletedPayout?.snapshotDate || new Date(0)

  const result = await IncomeLog.aggregate([
    {
      $match: {
        uid,
        status: 1,
        actualAt: {$gt: fromDate},
        // Include both 'earn' and 'claim' tabs
      },
    },
    {
      $group: {
        _id: null,
        unpaidBalance: {$sum: '$value'},
      },
    },
  ])

  return result[0]?.unpaidBalance || 0
}
```

### Revised:

```ts
// Simply use the already-calculated balance
usersWithBalance = db.walletBalance.find({availableBalance: {$gt: 0}})

forEach((user) => {
  // Create payout transaction
  db.payoutTransactions.insert({
    userId: user.uid,
    amount: user.availableBalance,
    status: 'pending',
    scheduledPayoutDate: '4th next month',
  })

  // Create claim log for audit trail
  db.incomeLog.insert({
    uid: user.uid,
    tab: 'claim',
    value: -user.availableBalance,
    status: 1,
    actualAt: now,
  })

  // Reset balance
  db.walletBalance.updateOne({uid: user.uid}, {$set: {availableBalance: 0}})
})
```

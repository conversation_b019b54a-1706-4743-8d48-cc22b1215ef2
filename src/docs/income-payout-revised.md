# Monthly Payout System Design & Implementation

## System Overview

A three-collection system for managing user income, wallet balances, and payouts:

1. **income-log**: Records all income transactions and claims
2. **wallet-balance**: Maintains current available balance per user
3. **payout-transactions**: Tracks scheduled and completed payouts

## Data Models

### Income Log Collection

```typescript
interface IncomeLog {
  uid: string
  tab: 'earn' | 'claim'
  category?: string
  value: number // Positive for earn, negative for claim
  expected: number
  actualAt: Date
  status: 0 | 1 // 0: expected, 1: actual
  needsWalletSync: boolean
  payoutTransactionId?: string // Link to payout transaction for claims
}
```

### Wallet Balance Collection

```typescript
interface WalletBalance {
  uid: string
  balanceType: 'income' | 'commission'
  availableBalance: number
}
```

### Payout Transactions Collection

```typescript
interface PayoutTransaction {
  userId: string
  amount: number
  status: 'pending' | 'completed' | 'failed'
  snapshotDate: Date // When wallet balance snapshot was taken
  scheduledPayoutDate: Date // When payout is scheduled to be sent
  userBankDetails: Record<string, any>
  errorDetails?: Record<string, any>
  sentAt?: Date
  completedAt?: Date
}
```

## Cron Job 1: 28th of Every Month (Payout Scheduling)

### Purpose

- Take snapshot of all user balances
- Schedule payouts for 4th of next month
- Create audit trail with claim logs
- Reset wallet balances

### Implementation

```typescript
import {addDays, startOfMonth, addMonths} from 'date-fns'

async function scheduleMonthlyPayouts() {
  console.log('Starting monthly payout scheduling...')

  try {
    // Find all users with available balance > 0
    const usersWithBalance = await db
      .collection('wallet-balance')
      .find({availableBalance: {$gt: 0}})
      .toArray()

    console.log(`Found ${usersWithBalance.length} users with available balance`)

    // Calculate next month's 4th date
    const nextMonth = addMonths(new Date(), 1)
    const scheduledPayoutDate = new Date(nextMonth.getFullYear(), nextMonth.getMonth(), 4)

    const batch = []

    for (const user of usersWithBalance) {
      const now = new Date()

      // Create payout transaction
      const payoutTransaction: PayoutTransaction = {
        userId: user.uid,
        amount: user.availableBalance,
        status: 'pending',
        snapshotDate: now,
        scheduledPayoutDate,
        userBankDetails: await getUserBankDetails(user.uid),
      }

      const payoutResult = await db.collection('payout-transactions').insertOne(payoutTransaction)

      // Create claim log for audit trail
      const claimLog: IncomeLog = {
        uid: user.uid,
        tab: 'claim',
        value: -user.availableBalance,
        expected: -user.availableBalance,
        actualAt: now,
        status: 1,
        needsWalletSync: true,
        payoutTransactionId: payoutResult.insertedId.toString(),
      }

      batch.push(
        // Insert claim log
        db.collection('income-log').insertOne(claimLog),

        // Reset wallet balance
        db.collection('wallet-balance').updateOne({uid: user.uid}, {$set: {availableBalance: 0}})
      )

      console.log(`Scheduled payout for user ${user.uid}: $${user.availableBalance}`)
    }

    // Execute all operations
    await Promise.all(batch)

    console.log('Monthly payout scheduling completed successfully')
  } catch (error) {
    console.error('Error in monthly payout scheduling:', error)
    throw error
  }
}

async function getUserBankDetails(uid: string) {
  // Implement your logic to fetch user bank details
  return await db.collection('users').findOne({uid}, {projection: {bankDetails: 1}})
}
```

## Cron Job 2: 4th of Every Month (Payout Processing)

### Purpose

- Process pending payouts scheduled for today or earlier
- Send money to user bank accounts
- Update payout status and completion timestamps
- Handle failures and errors

### Implementation

```typescript
async function processScheduledPayouts() {
  console.log('Starting scheduled payout processing...')

  try {
    const today = new Date()
    today.setHours(23, 59, 59, 999) // End of day

    // Find pending payouts due today or earlier
    const pendingPayouts = await db
      .collection('payout-transactions')
      .find({
        status: 'pending',
        scheduledPayoutDate: {$lte: today},
      })
      .toArray()

    console.log(`Found ${pendingPayouts.length} pending payouts to process`)

    for (const payout of pendingPayouts) {
      try {
        console.log(`Processing payout ${payout._id} for user ${payout.userId}: $${payout.amount}`)

        // Process bank transfer
        const transferResult = await processBankTransfer(payout)

        if (transferResult.success) {
          // Update payout transaction as completed
          await db.collection('payout-transactions').updateOne(
            {_id: payout._id},
            {
              $set: {
                status: 'completed',
                completedAt: new Date(),
                sentAt: transferResult.sentAt,
              },
            }
          )

          console.log(`Payout ${payout._id} completed successfully`)
        } else {
          // Handle failure
          await db.collection('payout-transactions').updateOne(
            {_id: payout._id},
            {
              $set: {
                status: 'failed',
                errorDetails: {
                  error: transferResult.error,
                  failedAt: new Date(),
                  retryCount: (payout.errorDetails?.retryCount || 0) + 1,
                },
              },
            }
          )

          console.error(`Payout ${payout._id} failed:`, transferResult.error)
        }
      } catch (error) {
        console.error(`Error processing payout ${payout._id}:`, error)

        // Mark as failed
        await db.collection('payout-transactions').updateOne(
          {_id: payout._id},
          {
            $set: {
              status: 'failed',
              errorDetails: {
                error: error.message,
                failedAt: new Date(),
                retryCount: (payout.errorDetails?.retryCount || 0) + 1,
              },
            },
          }
        )
      }
    }

    console.log('Scheduled payout processing completed')
  } catch (error) {
    console.error('Error in scheduled payout processing:', error)
    throw error
  }
}

interface TransferResult {
  success: boolean
  sentAt?: Date
  error?: string
  transactionId?: string
}

async function processBankTransfer(payout: PayoutTransaction): Promise<TransferResult> {
  // Implement your bank API integration here
  try {
    // Example bank API call
    const response = await bankAPI.transfer({
      amount: payout.amount,
      recipientAccount: payout.userBankDetails,
      reference: `Payout-${payout._id}`,
    })

    return {
      success: true,
      sentAt: new Date(),
      transactionId: response.transactionId,
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
    }
  }
}
```

## Cron Job Setup

### Using node-cron

```typescript
import cron from 'node-cron'

// Run on 28th of every month at 2:00 AM
cron.schedule('0 2 28 * *', async () => {
  console.log('Running monthly payout scheduling job...')
  await scheduleMonthlyPayouts()
})

// Run on 4th of every month at 3:00 AM
cron.schedule('0 3 4 * *', async () => {
  console.log('Running payout processing job...')
  await processScheduledPayouts()
})
```

### Alternative: Using agenda.js

```typescript
import Agenda from 'agenda'

const agenda = new Agenda({db: {address: mongoConnectionString}})

// Define jobs
agenda.define('schedule monthly payouts', async (job) => {
  await scheduleMonthlyPayouts()
})

agenda.define('process scheduled payouts', async (job) => {
  await processScheduledPayouts()
})

// Schedule recurring jobs
await agenda.start()
await agenda.every('0 2 28 * *', 'schedule monthly payouts')
await agenda.every('0 3 4 * *', 'process scheduled payouts')
```

## Error Handling & Monitoring

### Add logging and notifications

```typescript
interface PayoutJobResult {
  success: boolean
  usersProcessed: number
  totalAmount: number
  errors: string[]
}

async function scheduleMonthlyPayoutsWithMonitoring(): Promise<PayoutJobResult> {
  const result: PayoutJobResult = {
    success: false,
    usersProcessed: 0,
    totalAmount: 0,
    errors: [],
  }

  try {
    const usersWithBalance = await db
      .collection('wallet-balance')
      .find({availableBalance: {$gt: 0}})
      .toArray()

    result.usersProcessed = usersWithBalance.length
    result.totalAmount = usersWithBalance.reduce((sum, user) => sum + user.availableBalance, 0)

    // Process payouts...
    await scheduleMonthlyPayouts()

    result.success = true

    // Send success notification
    await sendNotification({
      type: 'payout_scheduling_success',
      message: `Successfully scheduled payouts for ${result.usersProcessed} users, total: $${result.totalAmount}`,
    })
  } catch (error) {
    result.errors.push(error.message)

    // Send error notification
    await sendNotification({
      type: 'payout_scheduling_error',
      message: `Payout scheduling failed: ${error.message}`,
    })
  }

  return result
}
```

## Key Benefits of This Approach

1. **Simple & Efficient**: Leverages existing wallet-balance reconciliation system
2. **Audit Trail**: Complete transaction history with claim logs
3. **Error Handling**: Proper failure management and retry logic
4. **Monitoring**: Built-in logging and notification system
5. **Scalable**: Processes users in batches, handles large volumes
6. **Data Integrity**: Maintains consistency across all collections

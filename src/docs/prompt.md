1. income-log collection:

```json
{
  uid: "user123",
  tab: "earn",
  category: "teaching_service",
  value: 100,
  expected: 100,
  actualAt: new Date(),
  status: 1, // 0: expected, 1: actual
  needsWalletSync: true,
}
```

2. wallet-balance collection:

```json
{
  "uid": "user123",
  "balanceType": "income",
  "availableBalance": 100
}
```

3. payout-transactions collection:

```json
{
  "userId": "user123",
  "amount": 100, // Amount to be paid out
  "status": "pending", // pending, completed, failed
  "snapshotDate": timestamp when wallet balance snapshot was taken,
  "scheduledPayoutDate": timestamp when payout is scheduled to be sent,
  "userBankDetails": {...},
  "errorDetails": {...},
  "sentAt": null,
  "completedAt": null,
}
```

When income-log is created or status becomes 1, we set needsWalletSync: true
using cron job, every 15 mins, I reconcile: income-log(needsWalletSync: true) and add them to wallet-balance(availableBalance)

Now, every 28th of the month, I need to take out availableBalance from wallet-balance and move it to payout-transactions with status: pending.
Then using payout-transactions, every 4th of next month, we payout the already calculated amount to user's bank account and status becomes completed. Then add new entry to income-log with tab: claim and value: -payout.amount, accordingly subtracting from wallet-balance. I am also confused whether we should deduct from balance on 28th or 4th.

My doubt is how would we write logic for day 28th cron and also day 4th cron. Don't give detailed code. Simply give the simneple queries/ides of how to approach.

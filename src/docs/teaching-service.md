### Check how booking looks when school books

### Next

- recheck transaction error issue occurrance. might have to change some flows
- check when .session is set in service-booking [done]
- accordingly set income-log session_status [done]
- verify for other mentoring types [partial_done_later]
- actual log creation on cancel, complete
- shift the logic for substitute
- handle for workshops
- check how to determine premium workshop

**IMP** Currently we create income for type: mentoring and serviceRoles: 'mentoring', 'substitute', 'consultant'
**-**In future we need to handle for correcting
**-** Even substitute has type: mentoring, then why: 'substitute' in ServiceType

### Actual Income Teaching Service

- Session ends && After 24 hours
  - Check if teaching accident
  - There is accident:
    - If approved/rejected
    - Check takeaway sent
    - If sent, generate income (in accident approve/reject)
  - No Accident:
    - Check takeaway sent
    - If sent, generate income (in session 24 hours cron)
  - If takeaway not sent
    - send reminder notification
  - Later if takeaway sent
    - generate income (in takeaway send)

### Total balance chronology

<!-- - Actual: 300 -->

- Expected: 100
- AT Actual: pending
- TS Expected: 100

### TODO

- Workshop 24 hours income handle | New cron. [done]
- Shift order/libs/orderIncome to income-log. [done]
- Teaching accident income handle [done]
- Refund workshop teaching accident on approval [done]
- write cron for balance update [done]

- Reduce session price on child session cancellation(do in session.hooks itself)
- Test Overall Income

// service-pack-model.ts - A mongoose model
// @ts-nocheck
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'
import {SectionSchema} from './service-pack-section.schema'

export default function (app: Application): Model<any> {
  const modelName = 'servicePack'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      name: {type: String, trim: true}, // 服务包名称
      description: {type: String, trim: true}, // description
      cover: {type: String, trim: true}, // files._id
      coverName: {type: String, trim: true}, // 图片名称
      points: {type: [String], trim: true}, // selling points
      type: {type: String, required: true, enum: Agl.ServiceType}, // 服务类型
      serviceRoles: {type: String, enum: Agl.ServiceRoles}, // 服务角色
      mentoringType: {type: String, enum: Agl.MentoringType}, // 辅导类型
      countryCode: {type: [String], trim: true}, // 国家代码
      curriculum: {type: String, trim: true}, // curriculum.code
      subject: {type: [String], trim: true}, // subjects._id
      topic: [
        {
          // 用于 essay, teacherTraining等大纲层级 认证项, subjects.topic..._id || topic._id
          _id: {type: String, trim: true}, // subjects.topic..._id
          label: {type: [String], trim: true}, // subjects.topic...name
        },
      ],
      gradeGroup: {type: [String], trim: true}, // 年级组
      sections: {type: [SectionSchema], default: []},
      qualification: {type: String, trim: true}, // 服务包需要的资质
      // 主题服务包 支持关联多个课件 https://github.com/zran-nz/bug/issues/4861
      contentOrientatedEnable: {type: Boolean, default: false}, // type === 'mentoring' 专用
      contentOrientatedConfig: {
        price: {type: Number}, // 主题服务包的价格
        schoolPrice: {type: Number, trim: true}, // 给机构的价格 单次价格 *100，cc，美分
      },
      contentOrientated: [
        {
          premium: {type: String, trim: true}, // contentOrientated === true 才有，取认证过的精品课 service-auth._id
          subject: {type: String, trim: true}, // 学科_id https://github.com/zran-nz/bug/issues/5344
          times: {type: Number}, // 最少授课次数，必须大于0
          price: {type: Number, trim: true}, // 单次价格 *100，cc，美分
          schoolPrice: {type: Number, trim: true}, // 给机构的价格 单次价格 *100，cc，美分
          servicePack: {type: String}, // 捆绑的服务包, service-pack._id 服务包id
          message: {type: String}, // 认证课不正常的时候提示
        },
      ],
      consultant: {
        // 顾问配置
        type: {type: String, enum: Agl.ConsultantType}, // 顾问类型
        carerService: {type: String},
        servicePack: {type: String}, // 捆绑的服务包, service-pack._id 服务包id
      },
      interviewPack: {
        _id: {type: String}, // 面试捆绑的服务包, service-pack._id 服务包id
        times: {type: Number}, // 次数
      },
      carerPack: {
        _id: {type: String}, // 管家捆绑的服务包, service-pack._id 服务包id
        times: {type: Number}, // 次数
      },
      associatedTask: {
        _id: {type: String}, // 关联的任务, service-task._id 服务包id
        times: {type: Number}, // frequency
      },
      price: {type: Number, trim: true}, // 单次价格 *100，cc，美分   Single price *100, cc, cents
      discount: [
        // dicount according to the number of sections(count)
        {
          count: {type: Number}, // 数量   quantity
          discount: {type: Number}, // 折扣 %
          gifts: {type: Number, default: 0}, // 免费赠送的次数
        },
      ],
      discountConfig: {
        // 折扣配置    Discount configuration
        enable: {type: Boolean, default: false}, // 是否启用折扣     Whether to enable discounts
        end: {type: Date}, // 折扣截止时间    Discount expiration time
        discount: {type: Number}, // 主题服务包 统一折扣 %   Theme Service Package Flat Discount %
        associateTaskDiscount: {type: Number}, // Associte Task Discount %
      },
      freq: {type: Number, enum: [7, 14, 30, 120]}, // 每张的可用多少天
      duration: {type: Number}, // session duration 单位：分钟
      break: {type: Number}, // session break 单位：分钟
      status: {type: Boolean, default: false}, // 发布状态    Release status
      lastPublished: {type: Date}, // 上次发布时间
      count: {
        // 统计
        sold: {type: Number}, // 已售
        valid: {type: Number}, // 有效数量
        ticket: {type: Number}, // 有效代金券数量
      },
      attachments: [
        {
          // 图片/视频，附件，推广素材, 插入到课堂的时候，显示在素材中
          filename: {type: String, trim: true}, // 文件名
          mime: {type: String, trim: true}, // 文件 MIME
          hash: {type: String, trim: true}, // 文件SHA1, files._id
          videoType: {type: String, enum: Agl.ServicePackVideoType}, // 视频的类型
        },
      ],
      income: {type: Number, default: 0}, //收入总计,单位分
      statistic: [
        {
          count: {type: Number}, // 数量 服务包次数
          orderCount: {type: Number, default: 0}, // 订单购买次数
          income: {type: Number, default: 0}, //收入总计,单位分
          isSchool: {type: Boolean, default: false}, // 学校/个人购买
          type: {type: String, enum: ['lecture', 'mentor', 'all']}, // 购买类型
          city: {type: String}, // 线下 城市
        },
      ],
      salesTarget: {type: [String], enum: Agl.ServiceSalesTarget}, // 销售目标
      backgroundCheck: {type: Boolean, default: false}, // academic 是否启用背景审查
      requirements: {type: String, default: false}, // academic 是否启用背景审查描述
      requirementsItems: {type: [String]}, // academic requirements 二级数据
      interview: {type: Boolean, default: false}, // 是否启用面试
      splitSale: {type: Boolean, default: false}, // 是否可以分拆卖
      filled: {type: Boolean, default: false}, // All forms have been filled
      reason: {type: String}, // 下架原因
      keywords: {type: [String]}, // 关键词搜索用，[subject, topic, ...]

      isOnCampus: {type: Boolean, default: false}, // 线上/线下
      country: {type: String, trim: true},
      onCampusPrice: [
        {
          hash: {type: String, trim: true},
          city: {type: String, trim: true},
          price: {type: Number, trim: true}, // 单次价格 *100，cc，美分
          discount: [
            {
              count: {type: Number}, // 数量
              discount: {type: Number}, // 折扣 %
              gifts: {type: Number, default: 0}, // 免费赠送的次数
            },
          ],
        },
      ],
      packageType: {type: String, enum: Agl.ServicePackageType, default: 'nonLecture'}, // package type
      essayTemplatePackId: {type: String, trim: true}, // essay-template-service-pack._id
      subjectHardCode: {type: String, trim: true}, // subject hard code for essay template service
    },
    {
      timestamps: true,
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}

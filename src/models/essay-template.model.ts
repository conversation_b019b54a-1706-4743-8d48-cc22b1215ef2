// essay-template-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'essayTemplate'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      name: {type: String, index: true},
      school: {type: String}, //school id
      mode: {type: String, default: 'essay'}, //essay
      serviceType: {type: String, enum: Agl.MentoringType}, //service type id
      subject: {type: String}, //hardcoded in backend
      cover: {type: String},
      coverName: {type: String},
      associatedTasks: [{type: String}], //associated tasks linked with template
      sections: [{type: String}], //sections associated with template
      published: {type: Boolean, default: false}, //template published or not
    },
    {
      timestamps: true,
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}

// associate-task-template-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'associateTaskTemplate'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      name: {type: String}, // Name of the item or entity
      cover: {type: String},
      coverName: {type: String},
      pid: {type: String}, // Reference to essay template ID
      serviceType: {type: String, enum: Agl.MentoringType}, // Reference to service type
      subject: {type: String}, // Subject or category name
      type: {type: String, enum: ['main', 'general'], default: 'general'}, // Type or classification
      purpose: {type: String}, // Purpose or intended use
      verificationItems: [
        {
          // For syllabus-level vertification items such as essays and teacherTraining, subjects.topic..._id || topic._id
          _id: {type: String, trim: true}, // subjects.topic..._id
          label: {type: [String], trim: true}, // subjects.topic...name
        },
      ], // List of items required for verification
      sections: [{type: String}], // References to related section IDs
      price: {type: Number}, // Price value
      filled: {type: Boolean, default: false}, // Flag to indicate if filled
      setPriceFlag: {type: Boolean, default: false}, // Flag to indicate if price is set
      priceSettings: {type: Schema.Types.Mixed, default: {}}, //price setting object with qualification
    },
    {timestamps: true}
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}

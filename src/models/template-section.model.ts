// template-section-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'templateSection'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      name: {type: String}, // Name of the item or entity
      prompt: {type: String}, // A prompt or description text
      pid: {type: String}, //essay-template id or associate-tsk-template id as parent id
      essayId: {type: String}, // essay-template id
      atid: [{type: String}], //associate-tsk-template id
      price: [
        // Array of pricing details
        {
          qualification: {type: String}, // Qualification or category for the price
          associateTaskId: {type: String}, // Linked task identifier
          salesPrice: {type: Number}, // Selling price amount
          commisionRate: {type: Number}, // Commission rate percentage
        },
      ],
      notebook: {type: Boolean, default: false}, // Flag to indicate if it includes a notebook
      feedback: {type: Schema.Types.Mixed, default: {}}, // Flag to enable/disable feedback
      todoList: {type: Schema.Types.Mixed, default: {}}, // Flexible field to store to-do list data
      rubrics: {type: Schema.Types.Mixed, default: {status: false, data: {}}}, // Flexible field to store rubric/assessment criteria
      del: {type: Boolean, default: false}, //delete status
    },
    {
      timestamps: true,
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}

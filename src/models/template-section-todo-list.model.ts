// template-section-todo-list-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'templateSectionTodoList'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      atid: {type: String, required: true}, // associated task id
      sectionId: {type: String, required: true}, // section id
      text: {type: String, required: true}, //question
      type: {type: String, required: true}, // test or multiple
      choices: {type: Schema.Types.Mixed, default: []}, // coices array for multiple
      multi: {type: Boolean}, //multiple choices can be selected or not
      showAnswer: {type: Boolean}, // show nswer for multiple
    },
    {
      timestamps: true,
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}

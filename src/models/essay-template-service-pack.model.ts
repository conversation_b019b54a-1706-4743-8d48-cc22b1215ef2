// essay-template-service-pack-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'essayTemplateServicePack'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      id: {type: String, trim: true}, // essay-template._id
      name: {type: String, trim: true},
      serviceType: {type: String, enum: Agl.MentoringType},
      subject: {type: String, trim: true},
      totalAssociatedTasks: {type: Number, default: 0},
      mainAssociatedTask: {type: Object, default: {}},
      supportAssociatedTasks: {type: [Object], default: []},
      totalPrice: {
        price: {type: Number},
        organizationPrice: {type: Number, trim: true},
      },
    },
    {
      timestamps: true,
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}

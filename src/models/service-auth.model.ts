// service-auth-model.ts - A mongoose model
// @ts-nocheck
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'serviceAuth'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      uid: {type: String, index: true, required: true},
      type: {type: String, index: true, required: true, enum: Agl.ServiceType}, // 服务类型
      mentoringType: {type: String, index: true, enum: Agl.MentoringType}, // 辅导类型
      enable: {type: Boolean, index: true, required: true}, // 是否启用
      serviceRoles: {type: [String], index: true, enum: Agl.ServiceRoles, default: Agl.ServiceRoles}, // 可以服务的项目 #4586
      serviceRolesUser: {type: [String], enum: Agl.ServiceRoles, default: Agl.ServiceRoles}, // 用户选择的
      countryCode: {type: [String], index: true, trim: true}, // 国家代码
      curriculum: {type: String, index: true, trim: true}, // 大纲代码 or 自定义的大纲_id
      subject: {type: String, index: true, trim: true}, // subject._id or pd use subject.subjectCode
      gradeGroup: {type: [String], index: true, trim: true}, // 年级组
      grades: {type: [String], trim: true}, // 实际年级
      tags: {type: [String], index: true, trim: true}, // 标签
      unit: {
        // 认证课件
        _id: {type: String, trim: true}, // unit._id
        name: {type: String, trim: true}, // 课件名称
        price: {type: Number}, // 课件价格, 单位 分 = 互动题数量*20
      },
      ability: {type: String, trim: true}, // 学习能力 https://github.com/zran-nz/bug/issues/5030
      styles: {type: [String], trim: true}, // 认知风格
      otherStyles: {type: [String], trim: true}, // 其他风格
      unitSnapshot: {type: Schema.Types.Mixed}, // 认证unit快照 https://github.com/zran-nz/bug/issues/4861
      linkSnapshot: {type: Schema.Types.Mixed}, // unitl link 的课件快照列表
      topic: [
        // 用于 essay, teacherTraining等大纲层级 认证项
        {
          _id: {type: String, trim: true}, // subjects.topic..._id
          label: {type: [String], trim: true}, // subjects.topic...name
        },
      ],
      desc: {type: String, trim: true}, // 描述
      status: {type: Number, index: true, default: 0}, // 0: 未申请/Apply verification, 1:申请中/Under processing, 2: 通过/Verified, -1: 拒绝/Under processing
      readyForJob: {type: Boolean, default: false}, // ready for job
      approval: {
        // 审批信息
        submitted: {type: Date}, // 提交时间
        approved: {type: Date}, // 审核时间
        approver: {type: String}, // 审核人uid
      },
      attachments: [
        // 附件
        {
          filename: {type: String, trim: true},
          mime: {type: String, trim: true},
          hash: {type: String, trim: true},
          date: {type: Date}, // 上传时间
          type: {type: String, trim: true}, // 认证类型, conf.val.attachmentType
          size: {type: Number}, // 文件大小
        },
      ],
      versionId: {type: String, trim: true}, // 版本 #4846
      reason: {type: String, trim: true},
      inviter: {type: String, trim: true}, //分享人
      qualification: {type: String, trim: true}, // 审核时候选的资质 #4864
      feedback: {
        // 留言反馈
        message: {type: String}, // 用户留言内容
        date: {type: Date}, // 留言时间
        read: {type: Boolean, default: false}, // read status
        reply: {type: String}, // 后台回复内容
        replyDate: {type: Date},
        replyRead: {type: Boolean, default: false}, // read status
      },
      follower: {type: String}, // 跟进人 user._id
      followedAt: {type: Date, index: true}, // 开始跟进时间
      releasedAt: {type: Date}, // 上次释放时间
      schoolOfFollower: {type: String}, // school-plan._id 学校管理员所属学校
      // importUsers: {type: [String]}, // 老师预约排课购买的自动排课被取消后，需要加入，可以重复
      interviewInvited: {type: Boolean, default: false}, // 面试邀请是否发送
      interviewPack: {type: String}, // 面试服务包id
      interviewApply: {type: Boolean, default: false}, // 面试已预约
      takeaway: {type: String}, // takeaway
      takeawayId: {type: String}, // session-takeaway-snapshot._id
      takeawayCreatedAt: {type: Date},
    },
    {
      timestamps: true,
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}

import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'
import {Note as NoteDoc} from '../types/note'

export default function (app: Application): Model<NoteDoc> {
  const modelName = 'notes'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient

  const historySchema = new Schema(
    {
      note: {type: String},
      version: {type: String},
      savedBy: {type: String},
      updatedTime: {type: Date, default: Date.now},
    },
    {_id: false}
  )

  const schema = new Schema(
    {
      note: {type: String, default: ''},
      version: {type: String, default: '1'},
      collaborators: [{type: String}],
      history: [historySchema],
    },
    {
      timestamps: true,
    }
  )

  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<NoteDoc>(modelName, schema)
}

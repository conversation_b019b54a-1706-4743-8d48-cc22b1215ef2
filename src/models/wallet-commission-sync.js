async function syncCommissionBalance() {
  const users = await db
    .collection('users')
    .find({balance: {$gt: 0}})
    .select('balance')
    .lean()
  for (const user of users) {
    await db.collection('wallet-balance').create({
      uid: user._id.toString(),
      isSchool: false,
      balanceType: 'commission',
      availableBalance: user.balance,
    })
  }

  // same for school-plan
  const schoolPlans = await db
    .collection('school-plan')
    .find({balance: {$gt: 0}})
    .select('balance')
    .lean()
  for (const school of schoolPlans) {
    await db.collection('wallet-balance').create({
      uid: school._id.toString(),
      isSchool: true,
      balanceType: 'commission',
      availableBalance: school.balance,
    })
  }
}
